<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('字典数据列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="add()"
               shiro:hasAnyPermissions="jubao:politicalStatus:add,jubao:reporterLevel:add,jubao:reportedLevel:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasAnyPermissions="jubao:politicalStatus:edit,jubao:reporterLevel:edit,jubao:reportedLevel:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasAnyPermissions="jubao:politicalStatus:remove,jubao:reporterLevel:remove,jubao:reportedLevel:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    const dictType = [[${dictType.dictType}]];
    const editFlag = [[${@permission.hasAnyPermi('jubao:politicalStatus:edit,jubao:reporterLevel:edit,jubao:reportedLevel:edit')}]];
    const removeFlag = [[${@permission.hasAnyPermi('jubao:politicalStatus:remove,jubao:reporterLevel:remove,jubao:reportedLevel:remove')}]];
    const prefix = ctx + "system/dict/lite";
    $(function () {
        const options = {
            url: prefix + "/list",
            createUrl: prefix + "/add/{id}",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            queryParams: queryParams,
            sortName: "dictSort",
            sortOrder: "asc",
            modalName: "数据",
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'dictCode',
                    title: "编码",
                    visible: false
                },
                {
                    field: 'dictLabel',
                    title: "名称"
                },
                {
                    field: 'dictSort',
                    title: '序号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        const actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.dictCode + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dictCode + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(options);
    });

    function queryParams(params) {
        const search = $.table.queryParams(params);
        search.dictType = dictType;
        return search;
    }

    function add() {
        $.operate.add(dictType);
    }
</script>
</body>
</html>