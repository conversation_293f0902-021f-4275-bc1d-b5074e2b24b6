<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:insert="~{include :: header('新增字典数据')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m" id="form-dict-add">
		<input type="hidden" id="dictType" name="dictType" th:value="${dictType}">
		<input type="hidden" id="dictValue" name="dictValue" value="generated">
		<input type="hidden" id="isDefault" name="isDefault" value="Y">
		<input type="hidden" id="status" name="status" value="0">
		<div class="form-group">
			<label class="col-sm-3 control-label is-required">名称：</label>
			<div class="col-sm-8">
				<input class="form-control" type="text" name="dictLabel" id="dictLabel" required>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label is-required">序号：</label>
			<div class="col-sm-8">
				<input class="form-control" type="text" name="dictSort" required>
			</div>
		</div>
	</form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script type="text/javascript">
	const prefix = ctx + "system/dict/lite";

	$("#form-dict-add").validate({
		rules: {
			dictSort: {
				digits: true
			},
		},
		focusCleanup: true
	});

	function submitHandler() {
		if ($.validate.form()) {
			$.operate.save(prefix + "/add", $('#form-dict-add').serialize());
		}
	}
</script>
</body>
</html>
