<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>4.7.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>ruoyi-admin</artifactId>

    <description>
        web服务入口
    </description>
    <properties>
        <java.version>21</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- SpringBoot集成thymeleaf模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>xydlfy-ph</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>kotlin-stdlib-common</artifactId>
                    <groupId>org.jetbrains.kotlin</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>de.siegmar</groupId>
            <artifactId>logback-gelf</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.blutorange</groupId>
                <artifactId>closure-compiler-maven-plugin</artifactId>
                <version>2.32.0</version>
                <executions>
                    <execution>
                        <id>minify-js</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                        <configuration>
                            <encoding>UTF-8</encoding>
                            <baseSourceDir>${project.basedir}/src/main/resources</baseSourceDir>
                            <baseTargetDir>${project.build.directory}/classes</baseTargetDir>
                            <sourceDir>static</sourceDir>
                            <targetDir>static</targetDir>
                            <includes>
                                <include>ruoyi/**/*.js</include>
                            </includes>
                            <allowReplacingInputFiles>true</allowReplacingInputFiles>
                            <skipMerge>true</skipMerge>
                            <force>true</force>
                            <outputFilename>#{path}/#{basename}.#{extension}</outputFilename>
                            <closureEmitUseStrict>false</closureEmitUseStrict>
                            <closureLanguageIn>ECMASCRIPT_2020</closureLanguageIn>
                            <closureLanguageOut>ECMASCRIPT5</closureLanguageOut>
                            <closureCompilationLevel>SIMPLE_OPTIMIZATIONS</closureCompilationLevel>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>zyxcx</finalName>
    </build>

</project>
