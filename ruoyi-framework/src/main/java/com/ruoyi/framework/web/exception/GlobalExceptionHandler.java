package com.ruoyi.framework.web.exception;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.security.PermissionUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.shiro.authz.AuthorizationException;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.http.server.servlet.ServletUtil;
import org.mospital.jackson.JacksonKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 优化请求详情获取方法，添加追踪ID
     */
    private String getRequestDetails(HttpServletRequest request, String traceId) {
        Map<String, Object> requestDetailsMap = new HashMap<>();
        requestDetailsMap.put("traceId", traceId);
        requestDetailsMap.put("uri", request.getRequestURI());
        requestDetailsMap.put("method", request.getMethod());
        requestDetailsMap.put("headers", ServletUtil.getHeadersMap(request));
        requestDetailsMap.put("params", ServletUtil.getParams(request));
        requestDetailsMap.put("remoteAddr", ServletUtil.getClientIP(request));
        
        // 尝试获取请求体
        try {
            String body = ServletUtil.getBody(request);
            if (!body.isEmpty()) {
                requestDetailsMap.put("body", body);
            }
        } catch (Exception ignored) {
            // 忽略请求体读取异常
        }
        
        return JacksonKit.INSTANCE.writeValueAsString(requestDetailsMap);
    }

    /**
     * 权限校验异常（ajax请求返回json，redirect请求跳转页面）
     */
    @ExceptionHandler(AuthorizationException.class)
    public Object handleAuthorizationException(AuthorizationException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("权限校验失败: {}, {}", getRequestDetails(request, ""), e.getMessage(), e);
        }

        if (ServletUtils.isAjaxRequest(request)) {
            return AjaxResult.error(PermissionUtils.getMsg(e.getMessage()));
        } else {
            return new ModelAndView("error/unauth");
        }
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("不支持的请求方式: {}, 请求方法: {}", getRequestDetails(request, ""), e.getMethod(), e);
        }
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        if (log.isErrorEnabled()) {
            log.error("发生未知异常 [TraceId: {}]: {}", traceId, getRequestDetails(request, traceId), e);
        }
        return AjaxResult.error("系统繁忙，请稍后重试")
                .put("traceId", traceId);
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request) {
        String traceId = generateTraceId();
        if (log.isErrorEnabled()) {
            log.error("系统异常 [TraceId: {}]: {}", traceId, getRequestDetails(request, traceId), e);
        }
        return AjaxResult.error("系统繁忙，请稍后重试")
                .put("traceId", traceId);
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public Object handleServiceException(ServiceException e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error("业务异常: {}, {}", getRequestDetails(request, ""), e.getMessage(), e);
        }
        if (ServletUtils.isAjaxRequest(request)) {
            return AjaxResult.error(e.getMessage());
        } else {
            return new ModelAndView("error/service", "errorMessage", e.getMessage());
        }
    }

    /**
     * 参数验证异常处理
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult handleBindException(BindException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        log.error("参数验证异常 [TraceId: {}]: {}", traceId, getRequestDetails(request, traceId), e);
        return AjaxResult.error("请求参数有误，请检查输入")
                .put("traceId", traceId);
    }

    /**
     * 生成追踪ID
     */
    private String generateTraceId() {
        return IdUtil.fastSimpleUUID();
    }

}
