package com.ruoyi.framework.shiro.authc;

import com.ruoyi.common.utils.PinyinUtils;
import org.apache.shiro.authc.HostAuthenticationToken;
import org.apache.shiro.authc.RememberMeAuthenticationToken;

public class PscNurseToken implements RememberMeAuthenticationToken, HostAuthenticationToken {

    private String empno;
    private String name;
    private String host;

    public PscNurseToken(String empno, String name, String host) {
        this.empno = empno;
        this.name = name;
        this.host = host;
    }

    public String getEmpno() {
        return empno;
    }

    public String getName() {
        return name;
    }

    @Override
    public String getHost() {
        return host;
    }

    @Override
    public boolean isRememberMe() {
        return true;
    }

    @Override
    public Object getPrincipal() {
        return empno;
    }

    @Override
    public Object getCredentials() {
        return PinyinUtils.getFullPinyin(name, "", true);
    }

}
