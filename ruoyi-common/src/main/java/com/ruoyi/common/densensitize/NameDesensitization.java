package com.ruoyi.common.densensitize;

import com.github.desensitization.StringDesensitization;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;

/**
 * <AUTHOR>
 * 姓名脱敏
 */
public class NameDesensitization implements StringDesensitization {
    @Override
    public String desensitize(String name) {
        if (StrValidator.isBlank(name)) {
            return StrValidator.EMPTY;
        }

        return CharSequenceUtil.hide(name, 1, name.length());
    }
}
