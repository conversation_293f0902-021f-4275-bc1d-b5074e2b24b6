package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.WxSubscribeMessage;
import space.lzhq.ph.mapper.WxSubscribeMessageMapper;
import space.lzhq.ph.service.IWxSubscribeMessageService;

@Slf4j
@Service
@Validated
public class WxSubscribeMessageServiceImpl
        extends ServiceImpl<WxSubscribeMessageMapper, WxSubscribeMessage>
        implements IWxSubscribeMessageService {

    @Override
    public boolean existsByCompanionIdAndTypeAndSendStatus(
            String companionId,
            String type,
            boolean sendStatus
    ) {
        return lambdaQuery()
                .eq(WxSubscribeMessage::getCompanionId, companionId)
                .eq(WxSubscribeMessage::getType, type)
                .eq(WxSubscribeMessage::getSendStatus, sendStatus)
                .exists();
    }

}
