package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.JubaoCase;
import space.lzhq.ph.mapper.JubaoCaseMapper;
import space.lzhq.ph.service.IJubaoCaseService;

import java.util.List;

/**
 * 举报Service业务层处理
 */
@Service
public class JubaoCaseServiceImpl implements IJubaoCaseService {
    private final JubaoCaseMapper jubaoCaseMapper;

    public JubaoCaseServiceImpl(JubaoCaseMapper jubaoCaseMapper) {
        this.jubaoCaseMapper = jubaoCaseMapper;
    }

    /**
     * 查询举报
     *
     * @param id 举报主键
     * @return 举报
     */
    @Override
    public JubaoCase selectJubaoCaseById(Long id) {
        return jubaoCaseMapper.selectJubaoCaseById(id);
    }

    /**
     * 查询举报列表
     *
     * @param jubaoCase 举报
     * @return 举报
     */
    @Override
    public List<JubaoCase> selectJubaoCaseList(JubaoCase jubaoCase) {
        return jubaoCaseMapper.selectJubaoCaseList(jubaoCase);
    }

    @Override
    public List<JubaoCase> selectListByOpenId(String openId) {
        return jubaoCaseMapper.selectListByOpenId(openId);
    }

    /**
     * 新增举报
     *
     * @param jubaoCase 举报
     * @return 结果
     */
    @Override
    public int insertJubaoCase(JubaoCase jubaoCase) {
        jubaoCase.setCreateTime(DateUtils.getNowDate());
        return jubaoCaseMapper.insertJubaoCase(jubaoCase);
    }

    /**
     * 修改举报
     *
     * @param jubaoCase 举报
     * @return 结果
     */
    @Override
    public int updateJubaoCase(JubaoCase jubaoCase) {
        jubaoCase.setUpdateTime(DateUtils.getNowDate());
        return jubaoCaseMapper.updateJubaoCase(jubaoCase);
    }

    /**
     * 批量删除举报
     *
     * @param ids 需要删除的举报主键
     * @return 结果
     */
    @Override
    public int deleteJubaoCaseByIds(String ids) {
        return jubaoCaseMapper.deleteJubaoCaseByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除举报信息
     *
     * @param id 举报主键
     * @return 结果
     */
    @Override
    public int deleteJubaoCaseById(Long id) {
        return jubaoCaseMapper.deleteJubaoCaseById(id);
    }

    @Override
    public int acceptJubaoCaseByIds(String ids) {
        return jubaoCaseMapper.acceptJubaoCaseByIds(Convert.toStrArray(ids));
    }

    @Override
    public int finishJubaoCaseByIds(String ids) {
        return jubaoCaseMapper.finishJubaoCaseByIds(Convert.toStrArray(ids));
    }
}
