package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.MedicalRecordMailing;

import java.util.List;

/**
 * 病历邮寄Service接口
 *
 * <AUTHOR>
 * @date 2023-11-11
 */
public interface IMedicalRecordMailingService extends IService<MedicalRecordMailing> {
    /**
     * 查询病历邮寄
     *
     * @param id 病历邮寄主键
     * @return 病历邮寄
     */
    public MedicalRecordMailing selectMedicalRecordMailingById(Long id);

    /**
     * 查询病历邮寄列表
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 病历邮寄集合
     */
    public List<MedicalRecordMailing> selectMedicalRecordMailingList(MedicalRecordMailing medicalRecordMailing);

    /**
     * 新增病历邮寄
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 结果
     */
    public int insertMedicalRecordMailing(MedicalRecordMailing medicalRecordMailing);

    /**
     * 修改病历邮寄
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 结果
     */
    public int updateMedicalRecordMailing(MedicalRecordMailing medicalRecordMailing);

    /**
     * 批量删除病历邮寄
     *
     * @param ids 需要删除的病历邮寄主键集合
     * @return 结果
     */
    public int deleteMedicalRecordMailingByIds(String ids);

    /**
     * 删除病历邮寄信息
     *
     * @param id 病历邮寄主键
     * @return 结果
     */
    public int deleteMedicalRecordMailingById(Long id);
}
