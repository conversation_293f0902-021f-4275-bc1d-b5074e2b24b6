package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscNurseSession;
import space.lzhq.ph.mapper.PscNurseSessionMapper;
import space.lzhq.ph.service.IPscNurseSessionService;

import java.util.List;

/**
 * 护士会话Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-12
 */
@Service
public class PscNurseSessionServiceImpl implements IPscNurseSessionService {
    @Autowired
    private PscNurseSessionMapper pscNurseSessionMapper;

    /**
     * 查询护士会话
     *
     * @param id 护士会话ID
     * @return 护士会话
     */
    @Override
    public PscNurseSession selectPscNurseSessionById(Long id) {
        return pscNurseSessionMapper.selectPscNurseSessionById(id);
    }

    @Override
    public PscNurseSession selectPscNurseSessionByEmployeeNoAndClientType(String employeeNo, Integer clientType) {
        return pscNurseSessionMapper.selectPscNurseSessionByEmployeeNoAndClientType(employeeNo, clientType);
    }

    @Override
    public PscNurseSession selectPscNurseSessionByOpenId(String openId) {
        return pscNurseSessionMapper.selectPscNurseSessionByOpenId(openId);
    }

    /**
     * 查询护士会话列表
     *
     * @param pscNurseSession 护士会话
     * @return 护士会话
     */
    @Override
    public List<PscNurseSession> selectPscNurseSessionList(PscNurseSession pscNurseSession) {
        return pscNurseSessionMapper.selectPscNurseSessionList(pscNurseSession);
    }

    @Override
    public boolean existsByEmployeeNoAndClientType(String employeeNo, Integer clientType) {
        return pscNurseSessionMapper.existsByEmployeeNoAndClientType(employeeNo, clientType);
    }

    /**
     * 新增护士会话
     *
     * @param pscNurseSession 护士会话
     * @return 结果
     */
    @Override
    public int insertPscNurseSession(PscNurseSession pscNurseSession) {
        return pscNurseSessionMapper.insertPscNurseSession(pscNurseSession);
    }

    /**
     * 修改护士会话
     *
     * @param pscNurseSession 护士会话
     * @return 结果
     */
    @Override
    public int updatePscNurseSession(PscNurseSession pscNurseSession) {
        return pscNurseSessionMapper.updatePscNurseSession(pscNurseSession);
    }

    /**
     * 删除护士会话对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePscNurseSessionByIds(String ids) {
        return pscNurseSessionMapper.deletePscNurseSessionByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除护士会话信息
     *
     * @param id 护士会话ID
     * @return 结果
     */
    @Override
    public int deletePscNurseSessionById(Long id) {
        return pscNurseSessionMapper.deletePscNurseSessionById(id);
    }

    @Override
    public int deletePscNurseSessionByOpenId(String openId) {
        return pscNurseSessionMapper.deletePscNurseSessionByOpenId(openId);
    }
}
