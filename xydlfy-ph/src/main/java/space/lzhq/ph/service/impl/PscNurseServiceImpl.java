package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscNurse;
import space.lzhq.ph.mapper.PscNurseMapper;
import space.lzhq.ph.service.IPscNurseService;

import java.util.List;

/**
 * 护士Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Service
public class PscNurseServiceImpl implements IPscNurseService {
    @Autowired
    private PscNurseMapper pscNurseMapper;

    /**
     * 查询护士
     *
     * @param id 护士ID
     * @return 护士
     */
    @Override
    public PscNurse selectPscNurseById(Long id) {
        return pscNurseMapper.selectPscNurseById(id);
    }

    @Override
    public PscNurse selectPscNurseByEmployeeNo(String employeeNo) {
        return pscNurseMapper.selectPscNurseByEmployeeNo(employeeNo);
    }

    /**
     * 查询护士列表
     *
     * @param pscNurse 护士
     * @return 护士
     */
    @Override
    public List<PscNurse> selectPscNurseList(PscNurse pscNurse) {
        return pscNurseMapper.selectPscNurseList(pscNurse);
    }

    @Override
    public boolean isEmployeeNoUnique(PscNurse pscNurse) {
        return pscNurseMapper.isEmployeeNoUnique(pscNurse);
    }

    @Override
    public boolean isMobileNoUnique(PscNurse pscNurse) {
        return pscNurseMapper.isMobileNoUnique(pscNurse);
    }

    /**
     * 新增护士
     *
     * @param pscNurse 护士
     * @return 结果
     */
    @Override
    public int insertPscNurse(PscNurse pscNurse) {
        return pscNurseMapper.insertPscNurse(pscNurse);
    }

    /**
     * 修改护士
     *
     * @param pscNurse 护士
     * @return 结果
     */
    @Override
    public int updatePscNurse(PscNurse pscNurse) {
        return pscNurseMapper.updatePscNurse(pscNurse);
    }

    /**
     * 删除护士对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePscNurseByIds(String ids) {
        return pscNurseMapper.deletePscNurseByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除护士信息
     *
     * @param id 护士ID
     * @return 结果
     */
    @Override
    public int deletePscNurseById(Long id) {
        return pscNurseMapper.deletePscNurseById(id);
    }
}
