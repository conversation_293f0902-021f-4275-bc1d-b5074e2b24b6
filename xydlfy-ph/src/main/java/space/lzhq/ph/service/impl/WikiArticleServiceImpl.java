package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.html.EscapeUtil;
import com.ruoyi.framework.util.ShiroUtils;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.WikiArticle;
import space.lzhq.ph.mapper.WikiArticleMapper;
import space.lzhq.ph.service.IWikiArticleService;

import java.util.Date;
import java.util.List;

/**
 * 文章Service业务层处理
 */
@Service
public class WikiArticleServiceImpl implements IWikiArticleService {

    private final WikiArticleMapper articleMapper;

    public WikiArticleServiceImpl(WikiArticleMapper articleMapper) {
        this.articleMapper = articleMapper;
    }

    /**
     * 查询文章
     *
     * @param id 文章ID
     * @return 文章
     */
    @Override
    public WikiArticle selectWikiArticleById(Long id) {
        return articleMapper.selectWikiArticleById(id);
    }

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章
     */
    @Override
    public List<WikiArticle> selectWikiArticleList(WikiArticle article) {
        return articleMapper.selectWikiArticleList(article);
    }

    @Override
    public List<WikiArticle> selectListByCategoryId(Long categoryId) {
        return articleMapper.selectListByCategoryId(categoryId);
    }

    private void cleanXss(WikiArticle article) {
        article.setTitle(EscapeUtil.clean(article.getTitle()));
        article.setCategoryName(EscapeUtil.clean(article.getCategoryName()));
        article.setSummary(EscapeUtil.clean(article.getSummary()));
    }

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int insertWikiArticle(WikiArticle article) {
        Date now = new Date();
        article.setCreateTime(now);
        article.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        article.setCreateBy(currentUserName);
        article.setUpdateBy(currentUserName);

        cleanXss(article);
        return articleMapper.insertWikiArticle(article);
    }

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int updateWikiArticle(WikiArticle article) {
        Date now = new Date();
        article.setCreateTime(now);
        article.setUpdateTime(now);

        String currentUserName = ShiroUtils.getLoginName();
        article.setCreateBy(currentUserName);
        article.setUpdateBy(currentUserName);

        cleanXss(article);
        return articleMapper.updateWikiArticle(article);
    }

    /**
     * 删除文章对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWikiArticleByIds(String ids) {
        return articleMapper.deleteWikiArticleByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除文章信息
     *
     * @param id 文章ID
     * @return 结果
     */
    @Override
    public int deleteWikiArticleById(Long id) {
        return articleMapper.deleteWikiArticleById(id);
    }
}
