package space.lzhq.ph.service;

import com.ruoyi.common.core.domain.Ztree;
import space.lzhq.ph.domain.DoctorCategory;

import java.util.List;

/**
 * 医生分类Service接口
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
public interface IDoctorCategoryService {
    /**
     * 查询医生分类
     *
     * @param id 医生分类主键
     * @return 医生分类
     */
    public DoctorCategory selectDoctorCategoryById(Long id);

    /**
     * 查询医生分类列表
     *
     * @param doctorCategory 医生分类
     * @return 医生分类集合
     */
    public List<DoctorCategory> selectDoctorCategoryList(DoctorCategory doctorCategory);

    /**
     * 新增医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    public int insertDoctorCategory(DoctorCategory doctorCategory);

    /**
     * 修改医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    public int updateDoctorCategory(DoctorCategory doctorCategory);

    /**
     * 批量删除医生分类
     *
     * @param ids 需要删除的医生分类主键集合
     * @return 结果
     */
    public int deleteDoctorCategoryByIds(String ids);

    /**
     * 删除医生分类信息
     *
     * @param id 医生分类主键
     * @return 结果
     */
    public int deleteDoctorCategoryById(Long id);

    /**
     * 关联医生分类信息
     *
     * @param doctorCategoryId 医生分类主键
     * @param doctorId         医生主键
     * @return 结果
     */
    public int associateDoctor(Long doctorCategoryId, String doctorId);

    /**
     * 添加科室和医生的关联关系
     *
     * @param departmentCode 科室编码
     * @param doctorId       医生ID
     */
    void addRelation(String departmentCode, String doctorId);

    /**
     * 查询医生分类树列表
     *
     * @return 所有医生分类信息
     */
    public List<Ztree> selectDoctorCategoryTree();
}
