package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.mapper.PscCarerMapper;
import space.lzhq.ph.service.IPscCarerService;

import java.util.List;

/**
 * 陪检Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Service
public class PscCarerServiceImpl implements IPscCarerService {
    @Autowired
    private PscCarerMapper pscCarerMapper;

    /**
     * 查询陪检
     *
     * @param id 陪检ID
     * @return 陪检
     */
    @Override
    public PscCarer selectPscCarerById(Long id) {
        return pscCarerMapper.selectPscCarerById(id);
    }

    @Override
    public PscCarer selectPscCarerByEmployeeNo(String employeeNo) {
        return pscCarerMapper.selectPscCarerByEmployeeNo(employeeNo);
    }

    /**
     * 查询陪检列表
     *
     * @param pscCarer 陪检
     * @return 陪检
     */
    @Override
    public List<PscCarer> selectPscCarerList(PscCarer pscCarer) {
        return pscCarerMapper.selectPscCarerList(pscCarer);
    }

    @Override
    public boolean isEmployeeNoUnique(PscCarer pscCarer) {
        return pscCarerMapper.isEmployeeNoUnique(pscCarer);
    }

    @Override
    public boolean isMobileNoUnique(PscCarer pscCarer) {
        return pscCarerMapper.isMobileNoUnique(pscCarer);
    }

    /**
     * 新增陪检
     *
     * @param pscCarer 陪检
     * @return 结果
     */
    @Override
    public int insertPscCarer(PscCarer pscCarer) {
        return pscCarerMapper.insertPscCarer(pscCarer);
    }

    /**
     * 修改陪检
     *
     * @param pscCarer 陪检
     * @return 结果
     */
    @Override
    public int updatePscCarer(PscCarer pscCarer) {
        return pscCarerMapper.updatePscCarer(pscCarer);
    }

    /**
     * 删除陪检对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePscCarerByIds(String ids) {
        return pscCarerMapper.deletePscCarerByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除陪检信息
     *
     * @param id 陪检ID
     * @return 结果
     */
    @Override
    public int deletePscCarerById(Long id) {
        return pscCarerMapper.deletePscCarerById(id);
    }
}
