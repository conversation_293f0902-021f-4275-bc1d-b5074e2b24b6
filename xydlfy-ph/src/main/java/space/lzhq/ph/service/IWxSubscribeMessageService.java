package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;
import space.lzhq.ph.domain.WxSubscribeMessage;

public interface IWxSubscribeMessageService extends IService<WxSubscribeMessage> {

    boolean existsByCompanionIdAndTypeAndSendStatus(
            @NotBlank String companionId,
            @NotBlank String type,
            boolean sendStatus
    );

}
