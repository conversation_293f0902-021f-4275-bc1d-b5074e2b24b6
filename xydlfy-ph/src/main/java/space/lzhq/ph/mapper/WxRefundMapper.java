package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.WxRefund;

import java.util.List;

/**
 * 退款记录Mapper接口
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Repository
public interface WxRefundMapper {
    /**
     * 查询退款记录
     *
     * @param id 退款记录ID
     * @return 退款记录
     */
    WxRefund selectWxRefundById(Long id);

    /**
     * 查询退款记录列表
     *
     * @param wxRefund 退款记录
     * @return 退款记录集合
     */
    List<WxRefund> selectWxRefundList(WxRefund wxRefund);

    /**
     * 新增退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    int insertWxRefund(WxRefund wxRefund);

    /**
     * 修改退款记录
     *
     * @param wxRefund 退款记录
     * @return 结果
     */
    int updateWxRefund(WxRefund wxRefund);

    /**
     * 删除退款记录
     *
     * @param id 退款记录ID
     * @return 结果
     */
    int deleteWxRefundById(Long id);

    /**
     * 批量删除退款记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWxRefundByIds(String[] ids);

    Long sumAmount(WxRefund wxRefund);
}
