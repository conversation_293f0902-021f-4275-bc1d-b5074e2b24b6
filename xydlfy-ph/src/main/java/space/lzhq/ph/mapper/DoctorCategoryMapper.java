package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.DoctorCategory;

import java.util.List;

/**
 * 医生分类Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
public interface DoctorCategoryMapper {
    /**
     * 查询医生分类
     *
     * @param id 医生分类主键
     * @return 医生分类
     */
    public DoctorCategory selectDoctorCategoryById(Long id);

    /**
     * 查询医生分类列表
     *
     * @param doctorCategory 医生分类
     * @return 医生分类集合
     */
    public List<DoctorCategory> selectDoctorCategoryList(DoctorCategory doctorCategory);

    /**
     * 使用医生ID查询医生分类
     */
    public List<DoctorCategory> selectByDoctorId(@Param("doctorId") String doctorId);

    /**
     * 新增医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    public int insertDoctorCategory(DoctorCategory doctorCategory);

    /**
     * 修改医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    public int updateDoctorCategory(DoctorCategory doctorCategory);

    /**
     * 删除医生分类
     *
     * @param id 医生分类主键
     * @return 结果
     */
    public int deleteDoctorCategoryById(Long id);

    /**
     * 批量删除医生分类
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDoctorCategoryByIds(String[] ids);

    /**
     * 医生分类关联医生
     *
     * @param doctorCategoryId 医生分类主键
     * @param doctorId         医生主键
     * @return 结果
     */
    public int associateDoctor(@Param("doctorCategoryId") Long doctorCategoryId, @Param("doctorId") String doctorId);
}
