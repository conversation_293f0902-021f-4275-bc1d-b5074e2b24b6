package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.MedicalRecordMailing;
import space.lzhq.ph.model.MedicalRecordMailingStatusEvent;
import space.lzhq.ph.service.IMedicalRecordMailingService;

import java.util.Date;
import java.util.List;

/**
 * 病历邮寄Controller
 *
 * <AUTHOR>
 * @date 2023-11-11
 */
@Controller
@RequestMapping("/ph/medical_record_mailing")
public class MedicalRecordMailingAdminController extends BaseController {
    private String prefix = "ph/medical_record_mailing";

    @Autowired
    private IMedicalRecordMailingService medicalRecordMailingService;

    @Autowired
    private ApplicationContext applicationContext;

    @RequiresPermissions("ph:medical_record_mailing:view")
    @GetMapping()
    public String medical_record_mailing() {
        return prefix + "/medical_record_mailing";
    }

    /**
     * 查询病历邮寄列表
     */
    @RequiresPermissions("ph:medical_record_mailing:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MedicalRecordMailing medicalRecordMailing) {
        startPage("id desc");
        List<MedicalRecordMailing> list = medicalRecordMailingService.selectMedicalRecordMailingList(medicalRecordMailing);
        return getDataTable(list);
    }

    /**
     * 导出病历邮寄列表
     */
    @RequiresPermissions("ph:medical_record_mailing:export")
    @Log(title = "病历邮寄", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MedicalRecordMailing medicalRecordMailing) {
        List<MedicalRecordMailing> list = medicalRecordMailingService.selectMedicalRecordMailingList(medicalRecordMailing);
        ExcelUtil<MedicalRecordMailing> util = new ExcelUtil<MedicalRecordMailing>(MedicalRecordMailing.class);
        return util.exportExcel(list, "病历邮寄数据");
    }

    /**
     * 新增病历邮寄
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存病历邮寄
     */
    @RequiresPermissions("ph:medical_record_mailing:add")
    @Log(title = "病历邮寄", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(MedicalRecordMailing medicalRecordMailing) {
        return toAjax(medicalRecordMailingService.insertMedicalRecordMailing(medicalRecordMailing));
    }

    /**
     * 修改病历邮寄
     */
    @RequiresPermissions("ph:medical_record_mailing:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        MedicalRecordMailing medicalRecordMailing = medicalRecordMailingService.selectMedicalRecordMailingById(id);
        mmap.put("medicalRecordMailing", medicalRecordMailing);
        return prefix + "/edit";
    }

    /**
     * 修改保存病历邮寄
     */
    @RequiresPermissions("ph:medical_record_mailing:edit")
    @Log(title = "病历邮寄", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(MedicalRecordMailing medicalRecordMailing) {
        if (medicalRecordMailing.getStatus() == MedicalRecordMailing.STATUS_CONFIRMED) {
            medicalRecordMailing.setConfirmedTime(new Date());
        }
        if (medicalRecordMailing.getStatus() == MedicalRecordMailing.STATUS_MAILED) {
            medicalRecordMailing.setMailedTime(new Date());
        }
        int updatedCode = medicalRecordMailingService.updateMedicalRecordMailing(medicalRecordMailing);

        MedicalRecordMailing updatedMedicalRecordMailing = medicalRecordMailingService.selectMedicalRecordMailingById(medicalRecordMailing.getId());
        applicationContext.publishEvent(new MedicalRecordMailingStatusEvent(updatedMedicalRecordMailing));
        return toAjax(updatedCode);
    }

    /**
     * 删除病历邮寄
     */
    @RequiresPermissions("ph:medical_record_mailing:remove")
    @Log(title = "病历邮寄", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(medicalRecordMailingService.deleteMedicalRecordMailingByIds(ids));
    }
}
