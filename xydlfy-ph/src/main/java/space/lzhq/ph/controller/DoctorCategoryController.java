package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.DoctorCategory;
import space.lzhq.ph.service.IDoctorCategoryService;
import space.lzhq.ph.service.IDoctorService;

import java.util.List;

/**
 * 医生分类Controller
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Controller
@RequestMapping("/ph/doctorCategory")
public class DoctorCategoryController extends BaseController {
    private String prefix = "ph/doctorCategory";

    @Autowired
    private IDoctorCategoryService doctorCategoryService;

    @Autowired
    private IDoctorService doctorService;

    @RequiresPermissions("ph:doctorCategory:view")
    @GetMapping()
    public String doctorCategory() {
        return prefix + "/doctorCategory";
    }

    /**
     * 查询医生分类树列表
     */
    @RequiresPermissions("ph:doctorCategory:list")
    @PostMapping("/list")
    @ResponseBody
    public List<DoctorCategory> list(DoctorCategory doctorCategory) {
        List<DoctorCategory> list = doctorCategoryService.selectDoctorCategoryList(doctorCategory);
        return list;
    }

    /**
     * 导出医生分类列表
     */
    @RequiresPermissions("ph:doctorCategory:export")
    @Log(title = "医生分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(DoctorCategory doctorCategory) {
        List<DoctorCategory> list = doctorCategoryService.selectDoctorCategoryList(doctorCategory);
        ExcelUtil<DoctorCategory> util = new ExcelUtil<DoctorCategory>(DoctorCategory.class);
        return util.exportExcel(list, "医生分类数据");
    }

    /**
     * 新增医生分类
     */
    @GetMapping(value = {"/add/{id}", "/add/"})
    public String add(@PathVariable(required = false) Long id, ModelMap mmap) {
        if (StringUtils.isNotNull(id)) {
            mmap.put("doctorCategory", doctorCategoryService.selectDoctorCategoryById(id));
        }
        return prefix + "/add";
    }

    /**
     * 新增保存医生分类
     */
    @RequiresPermissions("ph:doctorCategory:add")
    @Log(title = "医生分类", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(DoctorCategory doctorCategory) {
        return toAjax(doctorCategoryService.insertDoctorCategory(doctorCategory));
    }

    /**
     * 修改医生分类
     */
    @RequiresPermissions("ph:doctorCategory:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        DoctorCategory doctorCategory = doctorCategoryService.selectDoctorCategoryById(id);
        mmap.put("doctorCategory", doctorCategory);
        return prefix + "/edit";
    }

    /**
     * 修改保存医生分类
     */
    @RequiresPermissions("ph:doctorCategory:edit")
    @Log(title = "医生分类", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(DoctorCategory doctorCategory) {
        return toAjax(doctorCategoryService.updateDoctorCategory(doctorCategory));
    }

    /**
     * 删除
     */
    @RequiresPermissions("ph:doctorCategory:remove")
    @Log(title = "医生分类", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{id}")
    @ResponseBody
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(doctorCategoryService.deleteDoctorCategoryById(id));
    }

    /**
     * 选择医生分类树
     */
    @GetMapping(value = {"/selectDoctorCategoryTree/{id}", "/selectDoctorCategoryTree/"})
    public String selectDoctorCategoryTree(@PathVariable(required = false) Long id, ModelMap mmap) {
        if (StringUtils.isNotNull(id)) {
            mmap.put("doctorCategory", doctorCategoryService.selectDoctorCategoryById(id));
        }
        return prefix + "/tree";
    }

    /**
     * 加载医生分类树列表
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData() {
        List<Ztree> ztrees = doctorCategoryService.selectDoctorCategoryTree();
        return ztrees;
    }
}
