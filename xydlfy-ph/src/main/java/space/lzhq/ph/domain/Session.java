package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.common.ClientType;

import java.io.Serial;

/**
 * 微信会话对象 ph_wx_session
 *
 * @date 2020-05-24
 */
public class Session extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 客户端
     */
    private Integer clientType;

    /**
     * openid
     */
    @Excel(name = "openid")
    private String openId;

    /**
     * unionid
     */
    @Excel(name = "unionid")
    private String unionId;

    /**
     * session_key
     */
    @Excel(name = "session_key")
    private String sessionKey;

    /**
     * 支付宝智能消息授权访问令牌（Alipay Hospital Order Access Token）
     */
    private String ahoat;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public String getAhoat() {
        return ahoat;
    }

    public void setAhoat(String ahoat) {
        this.ahoat = ahoat;
    }

    public Boolean isWeixinSession() {
        return getClientType() == ClientType.WEIXIN.getCode();
    }

    public Boolean isAlipaySession() {
        return getClientType() == ClientType.ALIPAY.getCode();
    }

    public ClientType getClientTypeEnum() {
        return ClientType.Companion.fromCode(getClientType());
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clientType", getClientType())
                .append("openId", getOpenId())
                .append("unionId", getUnionId())
                .append("sessionKey", getSessionKey())
                .append("ahoat", getAhoat())
                .toString();
    }
}
