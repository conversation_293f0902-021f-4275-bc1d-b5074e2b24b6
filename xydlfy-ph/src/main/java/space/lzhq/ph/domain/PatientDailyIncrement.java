package space.lzhq.ph.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serial;
import java.util.Date;

public class PatientDailyIncrement extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    private Date date;

    private Long count;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("date", date)
                .append("count", count)
                .toString();
    }
}
