package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import space.lzhq.ph.common.ServiceType;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 充值记录对象 ph_wx_payment
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WxPayment extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型", dictType = "mz_zy")
    private String type;

    /**
     * openid
     */
    @Excel(name = "微信用户ID")
    private String openid;

    @Excel(name = "患者索引号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 掌医订单号
     */
    @Excel(name = "掌医订单号")
    private String zyPayNo;

    /**
     * 微信订单号
     */
    @Excel(name = "微信订单号")
    private String wxPayNo;

    /**
     * 微信交易状态
     */
    @Excel(name = "微信交易状态")
    private String wxTradeStatus;

    /**
     * 结算单号
     */
    @Excel(name = "结算单号")
    private String settlementIds;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    @Excel(name = "HIS收据号")
    private String hisReceiptNo;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long amount;

    /**
     * 已退金额
     * 1. 初始为0
     */
    @Excel(name = "已退金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long exrefund;

    /**
     * 未退金额
     * 1. 初始为null
     * 2. 收到支付成功的通知时，如当前值为初始值，则更新为订单金额
     */
    @Excel(name = "未退金额/分", cellType = Excel.ColumnType.NUMERIC, isStatistics = true)
    private Long unrefund;

    private BigDecimal balance;

    /**
     * 人工充值状态:0=未发起,1=待放行,2=已放行
     */
    @Excel(name = "手动充值状态", dictType = "manual_state")
    private Integer manualPayState;

    private String businessSerialNumber;

    public boolean isMenzhen() {
        return ServiceType.MZ.name().equals(getType());
    }

    public boolean isZhuyuan() {
        return ServiceType.ZY.name().equals(getType());
    }

    public boolean isZhenjian() {
        return ServiceType.ZJ.name().equals(getType());
    }

}
