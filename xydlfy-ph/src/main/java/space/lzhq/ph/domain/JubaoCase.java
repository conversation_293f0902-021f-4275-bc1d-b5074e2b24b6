package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.service.IJubaoCaseCategoryService;

import java.io.Serial;
import java.util.Date;
import java.util.Objects;

/**
 * 举报对象 jb_case
 */
@ExcelIgnoreUnannotated
public class JubaoCase extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 待受理
     */
    public static final String STATUS_INITED = "0";

    /**
     * 已受理
     */
    public static final String STATUS_ACCEPTED = "1";

    /**
     * 已办结
     */
    public static final String STATUS_FINISHED = "2";

    /**
     * 匿名举报
     */
    public static final int REPORTER_TYPE_ANONYMOUS = 0;

    /**
     * 实名举报
     */
    public static final int REPORTER_TYPE_REALNAME = 1;

    public static class ReporterTypeConverter implements Converter<Integer> {
        @Override
        public WriteCellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String text = "";
            if (Objects.equals(REPORTER_TYPE_ANONYMOUS, value)) {
                text = "匿名举报";
            } else if (Objects.equals(REPORTER_TYPE_REALNAME, value)) {
                text = "实名举报";
            }
            return new WriteCellData<>(text);
        }
    }

    public static class PoliticalStatusConverter implements Converter<String> {
        @Override
        public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String text = DictUtils.getDictLabel("politicalStatus", value);
            return new WriteCellData<>(text);
        }
    }

    public static class ReporterLevelConverter implements Converter<String> {
        @Override
        public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String text = DictUtils.getDictLabel("reporterLevel", value);
            return new WriteCellData<>(text);
        }
    }

    public static class ReportedLevelConverter implements Converter<String> {
        @Override
        public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String text = DictUtils.getDictLabel("reportedLevel", value);
            return new WriteCellData<>(text);
        }
    }

    public static class CategoryConverter implements Converter<Long> {
        @Override
        public WriteCellData<String> convertToExcelData(Long value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (value == null) {
                return new WriteCellData<>("");
            }
            IJubaoCaseCategoryService categoryService = SpringUtils.getBean(IJubaoCaseCategoryService.class);
            JubaoCaseCategory category = categoryService.selectJubaoCaseCategoryById(value);
            return category != null ? new WriteCellData<>(category.getName()) : new WriteCellData<>("");
        }
    }

    public static class StatusConverter implements Converter<String> {
        @Override
        public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String text = DictUtils.getDictLabel("jubao_status", value);
            return new WriteCellData<>(text);
        }
    }

    /**
     * ID
     */
    private Long id;

    /**
     * 客户端类型
     */
    private Integer clientType;

    /**
     * 用户ID
     */
    private String openId;

    /**
     * 举报人类型
     */
    @ExcelProperty(value = "举报类型", order = 10, converter = ReporterTypeConverter.class)
    @ColumnWidth(15)
    private Integer reporterType;

    /**
     * 举报人姓名
     */
    @ExcelProperty(value = {"举报人信息", "姓名"}, order = 20)
    @ColumnWidth(15)
    private String reporterName;

    /**
     * 举报人身份证号
     */
    @ExcelProperty(value = {"举报人信息", "身份证号"}, order = 30)
    @ColumnWidth(20)
    private String reporterIdCardNo;

    /**
     * 举报人手机号
     */
    @ExcelProperty(value = {"举报人信息", "手机号"}, order = 40)
    @ColumnWidth(20)
    private String reporterMobile;

    /**
     * 举报人现居住地址
     */
    @ExcelProperty(value = {"举报人信息", "现居住地址"}, order = 50)
    @ColumnWidth(30)
    private String reporterAddress;

    /**
     * 举报人政治面貌
     */
    @ExcelProperty(value = {"举报人信息", "政治面貌"}, order = 60, converter = PoliticalStatusConverter.class)
    @ColumnWidth(20)
    private String reporterPoliticalStatus;

    /**
     * 举报人级别
     */
    @ExcelProperty(value = {"举报人信息", "级别"}, order = 70, converter = ReporterLevelConverter.class)
    @ColumnWidth(15)
    private String reporterLevel;

    /**
     * 被举报人姓名
     */
    @ExcelProperty(value = {"被举报人信息", "姓名"}, order = 80)
    @ColumnWidth(15)
    private String reportedName;

    /**
     * 被举报人工作单位
     */
    @ExcelProperty(value = {"被举报人信息", "工作单位"}, order = 90)
    @ColumnWidth(20)
    private String reportedEmployer;

    /**
     * 被举报人职务
     */
    @ExcelProperty(value = {"被举报人信息", "职务"}, order = 100)
    @ColumnWidth(15)
    private String reportedPosition;

    /**
     * 被举报人级别
     */
    @ExcelProperty(value = {"被举报人信息", "级别"}, order = 110, converter = ReportedLevelConverter.class)
    @ColumnWidth(15)
    private String reportedLevel;

    /**
     * 问题类别
     */
    @ExcelProperty(value = {"举报问题", "类别"}, order = 120, converter = CategoryConverter.class)
    @ColumnWidth(40)
    private Long category1;

    /**
     * 问题细类
     */
    @ExcelProperty(value = {"举报问题", "细类"}, order = 130, converter = CategoryConverter.class)
    @ColumnWidth(40)
    private Long category2;

    /**
     * 标题
     */
    @ExcelProperty(value = {"举报问题", "标题"}, order = 140)
    @ColumnWidth(40)
    private String title;

    /**
     * 主要问题
     */
    @ExcelProperty(value = {"举报问题", "内容"}, order = 150)
    @ColumnWidth(50)
    private String content;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", order = 160, converter = StatusConverter.class)
    @ColumnWidth(15)
    private String status;

    /**
     * 举报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "举报时间", order = 170)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date createTime;

    /**
     * 受理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "受理时间", order = 180)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date acceptTime;

    /**
     * 办结时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "办结时间", order = 180)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date finishTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setReporterType(Integer reporterType) {
        this.reporterType = reporterType;
    }

    public Integer getReporterType() {
        return reporterType;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterIdCardNo(String reporterIdCardNo) {
        this.reporterIdCardNo = reporterIdCardNo;
    }

    public String getReporterIdCardNo() {
        return reporterIdCardNo;
    }

    public void setReporterMobile(String reporterMobile) {
        this.reporterMobile = reporterMobile;
    }

    public String getReporterMobile() {
        return reporterMobile;
    }

    public void setReporterAddress(String reporterAddress) {
        this.reporterAddress = reporterAddress;
    }

    public String getReporterAddress() {
        return reporterAddress;
    }

    public void setReporterPoliticalStatus(String reporterPoliticalStatus) {
        this.reporterPoliticalStatus = reporterPoliticalStatus;
    }

    public String getReporterPoliticalStatus() {
        return reporterPoliticalStatus;
    }

    public void setReporterLevel(String reporterLevel) {
        this.reporterLevel = reporterLevel;
    }

    public String getReporterLevel() {
        return reporterLevel;
    }

    public void setReportedName(String reportedName) {
        this.reportedName = reportedName;
    }

    public String getReportedName() {
        return reportedName;
    }

    public void setReportedEmployer(String reportedEmployer) {
        this.reportedEmployer = reportedEmployer;
    }

    public String getReportedEmployer() {
        return reportedEmployer;
    }

    public void setReportedPosition(String reportedPosition) {
        this.reportedPosition = reportedPosition;
    }

    public String getReportedPosition() {
        return reportedPosition;
    }

    public void setReportedLevel(String reportedLevel) {
        this.reportedLevel = reportedLevel;
    }

    public String getReportedLevel() {
        return reportedLevel;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setCategory1(Long category1) {
        this.category1 = category1;
    }

    public Long getCategory1() {
        return category1;
    }

    public void setCategory2(Long category2) {
        this.category2 = category2;
    }

    public Long getCategory2() {
        return category2;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setAttachments(String attachments) {
        this.attachments = attachments;
    }

    public String getAttachments() {
        return attachments;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clientType", getClientType())
                .append("openId", getOpenId())
                .append("reporterType", getReporterType())
                .append("reporterName", getReporterName())
                .append("reporterIdCardNo", getReporterIdCardNo())
                .append("reporterMobile", getReporterMobile())
                .append("reporterAddress", getReporterAddress())
                .append("reporterPoliticalStatus", getReporterPoliticalStatus())
                .append("reporterLevel", getReporterLevel())
                .append("reportedName", getReportedName())
                .append("reportedEmployer", getReportedEmployer())
                .append("reportedPosition", getReportedPosition())
                .append("reportedLevel", getReportedLevel())
                .append("title", getTitle())
                .append("category1", getCategory1())
                .append("category2", getCategory2())
                .append("content", getContent())
                .append("attachments", getAttachments())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("acceptTime", getAcceptTime())
                .append("finishTime", getFinishTime())
                .toString();
    }
}
