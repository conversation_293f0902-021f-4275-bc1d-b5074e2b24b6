<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.JubaoCaseCategoryMapper">

    <resultMap type="JubaoCaseCategory" id="JubaoCaseCategoryResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="sortNo" column="sort_no"/>
        <result property="parentName" column="parent_name"/>
    </resultMap>

    <sql id="selectJubaoCaseCategoryVo">
        select id, parent_id, name, sort_no from jb_case_category
    </sql>

    <select id="selectJubaoCaseCategoryList" parameterType="JubaoCaseCategory" resultMap="JubaoCaseCategoryResult">
        <include refid="selectJubaoCaseCategoryVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
        order by parent_id
    </select>

    <select id="selectJubaoCaseCategoryById" parameterType="Long" resultMap="JubaoCaseCategoryResult">
        select t.id, t.parent_id, t.name, t.sort_no, p.name as parent_name
        from jb_case_category t
        left join jb_case_category p on p.id = t.parent_id
        where t.id = #{id}
    </select>

    <insert id="insertJubaoCaseCategory" parameterType="JubaoCaseCategory" useGeneratedKeys="true" keyProperty="id">
        insert into jb_case_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sortNo != null">sort_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sortNo != null">#{sortNo},</if>
        </trim>
    </insert>

    <update id="updateJubaoCaseCategory" parameterType="JubaoCaseCategory">
        update jb_case_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sortNo != null">sort_no = #{sortNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJubaoCaseCategoryById" parameterType="Long">
        delete from jb_case_category where id = #{id}
    </delete>

    <delete id="deleteJubaoCaseCategoryByIds" parameterType="String">
        delete from jb_case_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>