<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.DoctorCategoryMapper">
    
    <resultMap type="DoctorCategory" id="DoctorCategoryResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sortNo"    column="sort_no"    />
        <result property="parentId"    column="parent_id"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="namePinyin"    column="name_pinyin"    />
        <result property="namePinyinFirst"    column="name_pinyin_first"    />
        <result property="remark"    column="remark"    />
        <result property="isSubspecialty"    column="is_subspecialty"    />
        <result property="parentName" column="parent_name" />
        <result property="address" column="address"/>
    </resultMap>

    <sql id="selectDoctorCategoryVo">
        select id,
               name,
               sort_no,
               parent_id,
               category_code,
               name_pinyin,
               name_pinyin_first,
               remark,
               is_subspecialty,
               address
        from doctor_category
    </sql>

    <select id="selectDoctorCategoryList" parameterType="DoctorCategory" resultMap="DoctorCategoryResult">
        <include refid="selectDoctorCategoryVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
            <if test="categoryCode != null  and categoryCode != ''">and category_code = #{categoryCode}</if>
            <if test="parentId != null">and parent_id = #{parentId}</if>
        </where>
        order by parent_id
    </select>
    
    <select id="selectDoctorCategoryById" parameterType="Long" resultMap="DoctorCategoryResult">
        select t.id,
               t.name,
               t.sort_no,
               t.parent_id,
               t.category_code,
               t.name_pinyin,
               t.name_pinyin_first,
               t.remark,
               t.is_subspecialty,
               t.address,
               p.name as parent_name
        from doctor_category t
                 left join doctor_category p on p.id = t.parent_id
        where t.id = #{id}
    </select>
    <select id="selectByDoctorId" resultType="space.lzhq.ph.domain.DoctorCategory">
        SELECT
            dc.*
        FROM
            doctor_category dc
                LEFT JOIN doctor_category_relationship dcr ON dc.id = dcr.doctor_category_id
        WHERE
            dcr.doctor_id =#{doctorId}
    </select>

    <insert id="insertDoctorCategory" parameterType="DoctorCategory" useGeneratedKeys="true" keyProperty="id">
        insert into doctor_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="sortNo != null">sort_no,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="categoryCode != null">category_code,</if>
            <if test="namePinyin != null">name_pinyin,</if>
            <if test="namePinyinFirst != null">name_pinyin_first,</if>
            <if test="remark != null">remark,</if>
            <if test="isSubspecialty != null">is_subspecialty,</if>
            <if test="address != null">address,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="sortNo != null">#{sortNo},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="categoryCode != null">#{categoryCode},</if>
            <if test="namePinyin != null">#{namePinyin},</if>
            <if test="namePinyinFirst != null">#{namePinyinFirst},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isSubspecialty != null">#{isSubspecialty},</if>
            <if test="address != null">#{address},</if>
         </trim>
    </insert>

    <update id="updateDoctorCategory" parameterType="DoctorCategory">
        update doctor_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sortNo != null">sort_no = #{sortNo},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryCode != null">category_code = #{categoryCode},</if>
            <if test="namePinyin != null">name_pinyin = #{namePinyin},</if>
            <if test="namePinyinFirst != null">name_pinyin_first = #{namePinyinFirst},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isSubspecialty != null">is_subspecialty = #{isSubspecialty},</if>
            <if test="address != null">address = #{address},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoctorCategoryById" parameterType="Long">
        delete from doctor_category where id = #{id}
    </delete>

    <delete id="deleteDoctorCategoryByIds" parameterType="String">
        delete from doctor_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="associateDoctor">
        INSERT INTO doctor_category_relationship (doctor_category_id, doctor_id) VALUES (#{doctorCategoryId}, #{doctorId})
    </insert>
</mapper>