<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('新增退款记录')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-refund-add">
        <div class="form-group">
            <label class="col-sm-3 control-label">类型：</label>
            <div class="col-sm-8">
                <select name="type" class="form-control m-b" th:with="type=${@dict.getType('mz_zy')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">openid：</label>
            <div class="col-sm-8">
                <input name="openid" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">就诊卡号：</label>
            <div class="col-sm-8">
                <input name="jzCardNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">住院号：</label>
            <div class="col-sm-8">
                <input name="zhuyuanNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">订单金额：</label>
            <div class="col-sm-8">
                <input name="amount" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">掌医充值订单号：</label>
            <div class="col-sm-8">
                <input name="zyPayNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">微信充值订单号：</label>
            <div class="col-sm-8">
                <input name="wxPayNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">掌医退款订单号：</label>
            <div class="col-sm-8">
                <input name="zyRefundNo" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">微信退款订单号：</label>
            <div class="col-sm-8">
                <input name="wxRefundNo" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">HIS交易状态：</label>
            <div class="col-sm-8">
                <textarea name="hisTradeStatus" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">微信交易状态：</label>
            <div class="col-sm-8">
                <textarea name="wxTradeStatus" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">人工退款状态：</label>
            <div class="col-sm-8">
                <select name="manualRefundState" class="form-control m-b"
                        th:with="type=${@dict.getType('manual_state')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script type="text/javascript">
    var prefix = ctx + "ph/refund"
    $("#form-refund-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-refund-add').serialize());
        }
    }
</script>
</body>
</html>