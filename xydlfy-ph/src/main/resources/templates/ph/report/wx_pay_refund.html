<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('充值退款')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>账单日：</label>
                            <input type="text" class="time-input" id="minId" placeholder="开始日期" th:value="${minId}"
                                   name="params[minId]" data-format="yyyyMMdd" data-callback="onSelectBillDate"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="maxId" placeholder="结束日期" th:value="${maxId}"
                                   name="params[maxId]" data-format="yyyyMMdd" data-callback="onSelectBillDate"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="checkThenSearch()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12" style="background: #fff; margin-top: 10px; border-radius: 6px;">
            <div class="echarts" id="echarts1"></div>
        </div>

        <div class="col-sm-12 select-table table-striped" style="min-height: 50%;">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: echarts-js}"/>
<th:block th:insert="~{include :: dayjs}"/>
<script th:inline="javascript">
    $(function () {
        const prefix = '/ph/report/wx_pay_refund';
        $.table.init({
            url: prefix,
            exportUrl: prefix + "/export",
            modalName: "充值退款报表",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showColumns: false,
            showToggle: true,
            showExport: true,
            showFooter: true,
            columns: [
                {
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'id',
                    title: '日期', footerFormatter: function () {
                        return '合计'
                    }
                },
                {
                    field: 'wxPayAmount',
                    title: '充值金额',
                    footerFormatter: function (rows) {
                        return rows.map(row => Number(row['wxPayAmount']) || 0).reduce((sum, x) => sum + x, 0).toFixed(2)
                    }
                },
                {
                    field: 'wxRefundAmount',
                    title: '退款金额',
                    footerFormatter: function (rows) {
                        return rows.map(row => Number(row['wxRefundAmount']) || 0).reduce((sum, x) => sum + x, 0).toFixed(2)
                    }
                },
                {
                    field: 'wxNetIn',
                    title: '净流入金额',
                    footerFormatter: function (rows) {
                        return rows.map(row => Number(row['wxNetIn']) || 0).reduce((sum, x) => sum + x, 0).toFixed(2)
                    }
                }
            ],
            onLoadSuccess: function (data) {
                renderChart(data.rows);
            }
        });
    });

    function renderChart(records) {
        var chart = echarts.init(document.getElementById('echarts1'), 'light');
        chart.setOption({
            legend: {},
            tooltip: {},
            dataset: {
                source: records.map(function (record) {
                    return {
                        "日期": record['id'],
                        "充值金额": record['wxPayAmount'],
                        "退款金额": record['wxRefundAmount'],
                        "净流入金额": record['wxNetIn']
                    };
                } )
            },
            xAxis: {
                type: 'category'
            },
            yAxis: {},
            series: [
                {type: 'bar'},
                {type: 'bar'},
                {type: 'bar'}
            ],
            toolbox: {
                show: true,
                feature: {
                    saveAsImage: {
                        show: true,
                        excludeComponents: ['toolbox'],
                        pixelRatio: 2
                    }
                }
            }
        })
        window.onresize = chart.resize;
    }

    function checkThenSearch() {
        var minId = $('#minId').val();
        var maxId = $('#maxId').val();
        if (!minId) {
            $.modal.msg('请选择开始日期');
            return;
        }
        if (!maxId) {
            $.modal.msg('请选择截止日期');
            return;
        }

        if (minId > maxId) {
            $.modal.msg('截止日期不应小于开始日期');
            $('#maxId').val('');
            return;
        }

        var minDate = dayjs(minId, 'YYYYMMDD');
        var maxDate = dayjs(maxId, 'YYYYMMDD');
        if (minDate.add(3, 'month').isBefore(maxDate)) {
            $.modal.msg('查询范围不应大于三个月');
            $('#maxId').val('');
            return;
        }

        $.table.search();
    }

    function onSelectBillDate() {
        var minId = $('#minId').val();
        var maxId = $('#maxId').val();
        if (minId && maxId) {
            if (minId > maxId) {
                $.modal.msg('截止日期不应小于开始日期');
                $('#maxId').val('');
                return;
            }

            var minDate = dayjs(minId, 'YYYYMMDD');
            var maxDate = dayjs(maxId, 'YYYYMMDD');
            if (minDate.add(3, 'month').isBefore(maxDate)) {
                $.modal.msg('查询范围不应大于三个月');
                $('#maxId').val('');
                return;
            }
        }
    }
</script>
</body>
</html>