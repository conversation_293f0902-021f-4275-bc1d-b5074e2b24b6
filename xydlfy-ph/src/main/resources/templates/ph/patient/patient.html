<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('就诊人列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>身份证号：</label>
                            <input type="text" name="idCardNo"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="jzCardNo"/>
                        </li>
                        <li>
                            <label>住院号：</label>
                            <input type="text" name="zhuyuanNo"/>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="mobile"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i> 搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i> 重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:patient:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:patient:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ph:patient:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:patient:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:patient:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:patient:remove')}]];
    var showSidFlag = [[${@permission.hasPermi('ph:patient:showSid')}]];
    var queryHisPatientFlag = [[${@permission.hasPermi('ph:patient:queryHisPatient')}]];
    var genderDatas = [[${@dict.getType('sys_user_sex')}]];
    var booleanDatas = [[${@dict.getType('yes_no')}]];
    var prefix = ctx + "ph/patient";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "就诊人",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'patientNo',
                    title: '患者索引号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号'
                },
                {
                    field: 'zhuyuanNo',
                    title: '住院号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'gender',
                    title: '性别',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(genderDatas, value);
                    }
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'guarderPhoneNumber',
                    title: '监护人手机号'
                },
                {
                    field: 'guarderName',
                    title: '监护人姓名'
                },
                {
                    field: 'guarderIDCard',
                    title: '监护人身份证号'
                },
                {
                    field: 'active',
                    title: '是否默认卡',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(booleanDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        // actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        actions.push('<a class="btn btn-info btn-xs ' + showSidFlag + '" href="javascript:void(0)" onclick="showSid(\'' + row.id + '\')"><i class="fa fa-eye"></i>查看SID</a> ');

                        if (row.jzCardNo) {
                            actions.push('<a class="btn btn-info btn-xs ' + queryHisPatientFlag + '" href="javascript:void(0)" onclick="queryHisPatient(\'' + row.jzCardNo + '\')"><i class="fa fa-eye"></i>查询HIS患者</a> ');
                        }

                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function showSid(id) {
        $.operate.get(prefix + '/showSid/' + id);
    }

    function queryHisPatient(jzCardNo) {
        $.operate.get(prefix + '/queryHisPatient/' + jzCardNo, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询HIS患者',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true" style="margin: 10px;">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function() {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }
</script>
</body>
</html>