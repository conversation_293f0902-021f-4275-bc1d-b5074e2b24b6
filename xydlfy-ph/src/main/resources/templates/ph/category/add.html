<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('新增栏目')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-category-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input name="name" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script type="text/javascript">
    var prefix = ctx + "ph/category"
    $("#form-category-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-category-add').serialize());
        }
    }
</script>
</body>
</html>