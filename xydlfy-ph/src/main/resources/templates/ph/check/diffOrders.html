<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('错单'+${billDate})}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/check";
    var id = [[${id}]];

    $(function () {
        $.table.init({
            url: prefix + "/diffOrders/" + id,
            modalName: "错单",
            pagination: false,
            columns: [
                {
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'orderType',
                    title: '订单类型'
                },
                {
                    field: 'orderNo',
                    title: '订单号'
                },
                {
                    field: 'patientId',
                    title: '患者索引号'
                },
                {
                    field: 'wxOrderTime',
                    title: '微信交易时间'
                },
                {
                    field: 'hisOrderTime',
                    title: 'HIS交易时间'
                },
                {
                    field: 'wxAmount',
                    title: '微信交易金额'
                },
                {
                    field: 'hisAmount',
                    title: 'HIS交易金额'
                },
                {
                    field: 'diffAmount',
                    title: '差额'
                }
            ]
        });
    });
</script>
</body>
</html>