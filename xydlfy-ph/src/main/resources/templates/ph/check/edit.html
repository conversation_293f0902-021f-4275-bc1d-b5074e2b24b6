<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改对账')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-check-edit" th:object="${wxCheck}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信支付总金额：</label>
                <div class="col-sm-8">
                    <input name="wxPayAmount" th:field="*{wxPayAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS充值总金额：</label>
                <div class="col-sm-8">
                    <input name="hisPayAmount" th:field="*{hisPayAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付差额：</label>
                <div class="col-sm-8">
                    <input name="diffPayAmount" th:field="*{diffPayAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付平账？：</label>
                <div class="col-sm-8">
                    <select name="payBalanced" class="form-control m-b" th:with="type=${@dict.getType('yes_no')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{payBalanced}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信退款总金额：</label>
                <div class="col-sm-8">
                    <input name="wxRefundAmount" th:field="*{wxRefundAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS退款总金额：</label>
                <div class="col-sm-8">
                    <input name="hisRefundAmount" th:field="*{hisRefundAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退款差额：</label>
                <div class="col-sm-8">
                    <input name="diffRefundAmount" th:field="*{diffRefundAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退款平账？：</label>
                <div class="col-sm-8">
                    <input name="refundBalanced" th:field="*{refundBalanced}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信净流入：</label>
                <div class="col-sm-8">
                    <input name="wxNetIn" th:field="*{wxNetIn}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">HIS净流入：</label>
                <div class="col-sm-8">
                    <input name="hisNetIn" th:field="*{hisNetIn}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "ph/check";
        $("#form-check-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-check-edit').serialize());
            }
        }
    </script>
</body>
</html>