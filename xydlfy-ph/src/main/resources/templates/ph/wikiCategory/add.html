<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('新增知识分类')}"/>
    <th:block th:insert="~{include :: bootstrap-fileinput-css}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-wikiCategory-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input name="name" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">图标：</label>
            <div class="col-sm-8">
                <input type="hidden" name="icon">
                <div class="file-loading">
                    <input class="form-control file-upload" id="icon" name="file" type="file">
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">排序号：</label>
            <div class="col-sm-8">
                <input name="sortNo" class="form-control" type="text" required value="0">
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: bootstrap-fileinput-js}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/wikiCategory"
    $("#form-wikiCategory-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-wikiCategory-add').serialize());
        }
    }

    $(".file-upload").fileinput({
        uploadUrl: ctx + 'common/upload',
        maxFileCount: 1,
        autoReplace: true,
        allowedFileTypes: ['image'],
        showUpload: false,
        browseOnZoneClick: true,
    }).on('filebatchselected', function (event, files) {
        $(this).fileinput("upload");
    }).on('fileuploaded', function (event, data, previewId, index) {
        $("input[name='" + event.currentTarget.id + "']").val(data.response.url)
    }).on('fileremoved', function (event, id, index) {
        $("input[name='" + event.currentTarget.id + "']").val('')
    })
</script>
</body>
</html>