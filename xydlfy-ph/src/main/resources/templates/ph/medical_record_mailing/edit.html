<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改病历邮寄')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form th:switch="${medicalRecordMailing.status}" class="form-horizontal m" id="form-medical_record_mailing-edit" th:object="${medicalRecordMailing}">
        <input name="id" th:field="*{id}" type="hidden">
        <input name="currentStatus" th:value="${medicalRecordMailing.status}" type="hidden">
        <!--      申请病历状态显示的字段      -->
        <div th:case="1">
            <div class="alert alert-info" role="alert">
                请确认病历申请信息后，填写病历页数、复印费用、快递费用信息。如用户填写信息有误，请仅填写拒绝原因。
            </div>
            <input name="status" th:value="2" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">病历页数：</label>
                <div class="col-sm-8">
                    <input name="medicalRecordPages" th:field="*{medicalRecordPages}" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">复印费用（单位为分）：</label>
                <div class="col-sm-8">
                    <input name="copyingFee" th:field="*{copyingFee}" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">快递费用（单位为分）：</label>
                <div class="col-sm-8">
                    <input name="expressFee" th:field="*{expressFee}" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">拒绝原因：</label>
                <div class="col-sm-8">
                    <textarea name="shutdownReason" th:field="*{shutdownReason}" class="form-control" placeholder="仅在需要拒绝用户病历申请时，填写该字段"></textarea>
                </div>
            </div>
        </div>

        <!--      已支付状态显示的字段      -->
        <div th:case="3" class="form-group">
            <div class="alert alert-info" role="alert">
                请填写快递单号信息
            </div>
            <input name="status" th:value="4" type="hidden">
            <label class="col-sm-3 control-label">快递单号：</label>
            <div class="col-sm-8">
                <input name="courierNumber" th:field="*{courierNumber}" class="form-control" type="text">
            </div>
        </div>

        <div th:case="*">
            <div class="alert alert-danger" role="alert">
                当前状态的订单无可用操作
            </div>
        </div>

    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/medical_record_mailing";
    $("#form-medical_record_mailing-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            let serialize = $('#form-medical_record_mailing-edit').serialize();
            let params = new URLSearchParams(serialize)
            if (params.get("currentStatus") == 1 && params.get("shutdownReason")) {
                params.delete("status")
                params.set("status", "9")
            }

            console.log(serialize)
            $.operate.save(prefix + "/edit", params.toString());
        }
    }
</script>
</body>
</html>