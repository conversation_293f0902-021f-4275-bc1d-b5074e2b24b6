<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('医生列表')}"/>
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>科室：</label>
                            <select name="departmentId">
                                <option value="">所有</option>
                                <!--/*@thymesVar id="departments" type="java.util.List<space.lzhq.ph.domain.Department>"*/-->
                                <option th:each="department:${departments}"
                                        th:value="${department.id}"
                                        th:text="${department.name}"
                                ></option>
                            </select>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>性别：</label>
                            <select name="sex" th:with="type=${@dict.getType('sys_user_sex')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>简拼：</label>
                            <input type="text" name="simplePinyin"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        </li>
                    </ul>
                    <div>
                        <label class="toggle-switch switch-solid">
                            <input type="checkbox" id="status" onclick="$.table.search()" checked>
                            <span></span>
                        </label>
                        <span>查看关联/关联医生</span>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:doctor:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:doctor:remove')}]];
    var sexDatas = [[${@dict.getType('sys_user_sex')}]];
    var prefix = ctx + "ph/doctor";

    $(function () {
        var options = {
            url: prefix + "/list",
            modalName: "医生",
            uniqueId: "id",
            queryParams: queryParams,
            columns: [{
                checkbox: true
            },
                {
                    field: 'departmentId',
                    title: '科室编码'
                },
                {
                    field: 'department.name',
                    title: '科室名称'
                },
                {
                    field: 'id',
                    title: '编码',
                    visible: true
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'sex',
                    title: '性别',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(sexDatas, value);
                    }
                },
                {
                    field: 'title',
                    title: '职称'
                },
                {
                    field: 'position',
                    title: '职务'
                },
                {
                    field: 'expertise',
                    title: '擅长'
                },
                {
                    field: 'intro',
                    title: '简介'
                },
                {
                    field: 'photo',
                    title: '照片',
                    formatter: function (value, row, index) {
                        return $.table.imageView(value, '', '', 'blank');
                    }
                }]
        };
        $.table.init(options);
    });

    function getDoctorCategoryId() {
        const searchParams = new URLSearchParams(window.location.search)
        return searchParams.get('doctorCategoryId')
    }

    function queryParams(params) {
        const search = $.table.queryParams(params);
        if (!$('#status').is(':checked') && getDoctorCategoryId()) {
            search.doctorCategoryId = getDoctorCategoryId()
        }
        return search;
    }

    function updateCell(field, row) {
        var form = {};
        form['id'] = row['id'];
        form[field] = row[field];
        $.operate.post(prefix + '/edit', form);
    }

    /**
     * 从 doctorCategory.html 选择医生过来，用于关联医生分类
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.operate.post(prefix + `/associateDoctor/${getDoctorCategoryId()}/${$.table.selectColumns('id')}`);
    }
</script>
</body>
</html>