<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <th:block th:insert="~{include :: header('验收')}"/>
    <link rel="stylesheet" href="/bootstrap-star-rating/css/star-rating.min.css" media="all" type="text/css">
    <link rel="stylesheet" href="/bootstrap-star-rating/themes/krajee-svg/theme.min.css" media="all" type="text/css">
    <style>
        #examItems-error {
            margin-top: -33px;
            margin-left: -120px;
        }
    </style>
</head>
<body class="white-bg">
<form class="form-horizontal m" id="form-pscOrder-confirm">
    <input type="hidden" name="id" th:field="${pscOrder.id}">
    <div class="form-group">
        <label class="col-sm-12 control-label is-required">检查项目：</label>
        <div class="col-sm-12" th:with="type=${@dict.getType('psc_exam_item')}">
            <label th:each="dict : ${type}" th:if="${pscOrder.examItems.contains(dict.dictValue)}" class="check-box"
                   style="display: block">
                <input name="examItems" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}"
                       checked required>
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-12 control-label is-required">评价：</label>
        <div class="col-sm-12">
            <input id="score" name="score" class="rating-loading" type="text" value="3" required>
        </div>
    </div>
</form>
<p style="color: red; padding: 20px;">
    温馨提示：<br/>
    请仔细确认检查项目，将按照以下规则进行验收：<br/>
    1. 勾选状态的检查项目将被验收。<br/>
    2. 取消勾选的检查项目，将继承当前工单的其它信息，并生成一个新的工单。新工单的状态为正在服务，请等待陪检人员服务完成再来验收。
</p>
<th:block th:insert="~{include :: footer}"/>
<script type="text/javascript" src="/bootstrap-star-rating/js/star-rating.min.js"></script>
<script type="text/javascript" src="/bootstrap-star-rating/themes/krajee-svg/theme.min.js"></script>
<script type="text/javascript" src="/bootstrap-star-rating/js/locales/zh.js"></script>
<script th:inline="javascript">
    $(function () {
        $('#score').rating({
            theme: 'krajee-svg',
            filledStar: '<span class="krajee-icon krajee-icon-star"></span>',
            emptyStar: '<span class="krajee-icon krajee-icon-star"></span>',
            min: 0,
            max: 5,
            step: 1,
            showClear: false,
            language: 'zh',
            starCaptions: {
                1: '非常不满意',
                2: '不满意',
                3: '一般',
                4: '满意',
                5: '非常满意'
            },
        });

        $("#form-pscOrder-confirm").validate({
            focusCleanup: true
        });
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(`/ph/pscOrder/confirm`, $('#form-pscOrder-confirm').serialize())
        }
    }
</script>
</body>
</html>
