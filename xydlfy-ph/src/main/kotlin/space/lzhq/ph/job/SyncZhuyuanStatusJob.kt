package space.lzhq.ph.job

import org.dromara.hutool.core.date.DateUtil
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.Result
import org.mospital.bsoft.ZhuyuanHistory
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.NurseCard
import space.lzhq.ph.domain.NurseCardStatus
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.service.INurseCardService
import space.lzhq.ph.service.IPatientService
import java.util.*

/**
 * 同步住院状态
 * 调用目标字符串：syncZhuyuanStatusJob.sync()
 * cron表达式：0 0 0/1 * * ?
 */
@Component("syncZhuyuanStatusJob")
class SyncZhuyuanStatusJob {

    @Autowired
    private lateinit var nurseCardService: INurseCardService

    @Autowired
    private lateinit var patientService: IPatientService

    private val log = LoggerFactory.getLogger(SyncZhuyuanStatusJob::class.java)

    fun sync() {
        nurseCardService.selectNurseCardList(NurseCard().apply {
            this.status = NurseCardStatus.NORMAL
        }).forEach {
            try {
                syncNurseCardStatus(it)
            } catch (e: Exception) {
                log.debug(e.message, e)
            }
        }
        patientService.selectZaiyuanPatient().forEach {
            try {
                syncPatientStatus(it)
            } catch (e: Exception) {
                log.debug(e.message, e)
            }
        }
    }

    private fun syncNurseCardStatus(nurseCard: NurseCard) {
        val zhuyuanHistoryListResult: Result<List<ZhuyuanHistory>> =
            BSoftService.getZhuyuanHistoryListByIdCardNo(nurseCard.patientIdCardNo)
        if (zhuyuanHistoryListResult.isOk().not()) {
            return
        }

        val zhuyuanHistory: ZhuyuanHistory? = zhuyuanHistoryListResult.data?.firstOrNull {
            it.admissionNo == nurseCard.zhuyuanNo
        }
        if (zhuyuanHistory == null) {
            return
        }

        if (zhuyuanHistory.leaveTime != null) {
            nurseCard.chuyuanTime = DateUtil.date(zhuyuanHistory.leaveTime)
            nurseCard.status = NurseCardStatus.EXPIRED
            nurseCard.expiredTime = Date()
            nurseCardService.updateNurseCard(nurseCard)
        }
    }

    private fun syncPatientStatus(patient: Patient) {
        val zhuyuanHistoryListResult: Result<List<ZhuyuanHistory>> =
            BSoftService.getZhuyuanHistoryListByIdCardNo(patient.idCardNo)
        if (zhuyuanHistoryListResult.isOk().not()) {
            return
        }

        val zhuyuanHistory: ZhuyuanHistory? = zhuyuanHistoryListResult.data?.firstOrNull {
            it.admissionNo == patient.zhuyuanNo
        }
        if (zhuyuanHistory == null) {
            return
        }

        if (zhuyuanHistory.leaveTime != null) {
            patient.ruyuanTime = DateUtil.parse(zhuyuanHistory.admissionTime as CharSequence)
            patient.chuyuanTime = DateUtil.date(zhuyuanHistory.leaveTime)
            patientService.updatePatient(patient)
        }
    }
}