package space.lzhq.ph.controller

import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.page.TableDataInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.domain.Material
import space.lzhq.ph.service.IMaterialService

@RestController
@RequestMapping("/open/material")
class MaterialApiController : BaseController() {

    @Autowired
    private lateinit var materialService: IMaterialService

    @GetMapping("/paginate")
    fun paginate(
        @RequestParam(defaultValue = "100") pageSize: Int,
        @RequestParam(defaultValue = "1") pageNumber: Int,
        @RequestParam(defaultValue = "") keyword: String,
    ): TableDataInfo {
        PageHelper.startPage<Material>(pageNumber, pageSize, "no asc")
        val queryTemplate = Material().apply {
            this.name = keyword
        }
        val list = materialService.selectMaterialList(queryTemplate)
        return getDataTable(list)
    }

}