package space.lzhq.ph.controller

import com.alipay.api.response.AlipayTradeCreateResponse
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult
import com.github.binarywang.wxpay.service.WxPayService
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.crypto.digest.MD5
import org.dromara.hutool.extra.spring.SpringUtil
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.bsoft.*
import org.mospital.yahua.YahuaService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.AlipayPayment
import space.lzhq.ph.domain.AlipayRefund
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.domain.WxRefund
import space.lzhq.ph.ext.*
import space.lzhq.ph.service.IAlipayPaymentService
import space.lzhq.ph.service.IAlipayRefundService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import java.math.BigDecimal
import java.time.LocalDate
import java.util.*
import java.util.Collections.max

@Suppress("DuplicatedCode")
@RestController
@RequestMapping("/api/menzhen")
class MenzhenController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    @Autowired
    private lateinit var alipayRefundService: IAlipayRefundService

    @GetMapping("cloudPACSUrl")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun cloudPACSUrl(): AjaxResult {
        val activePatient = request.getCurrentPatient()

        if (!IdcardUtil.isValidCard(activePatient.idCardNo)) {
            return AjaxResult.error("身份证号不正确")
        }

        // 医院ID
        val h = "106"
        // 医院编码
        val ewm = "zoCHqK4ruVDLEAOC8FygHLPG891z63KX"
        // 加密串
        val s = MD5.of().digestHex(h + ewm + activePatient.idCardNo)
        // 云胶片链接
        val url =
            "https://p.kndcloud.com/#/pages/patientExaminationInfo/checkList?h=$h&ewm=$ewm&idcard=${activePatient.idCardNo}&s=$s"

        return AjaxResult.success(mapOf<String, Any>("url" to url))
    }

    /**
     * 排队候诊
     */
    @GetMapping("queue")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getQueue(): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val queueResponse = runBlocking {
            YahuaService.me.getPatientQueue(
                patientKey = currentPatient.jzCardNo
            )
        }
        return if (queueResponse.isOk()) {
            AjaxResult.success(queueResponse.data)
        } else {
            AjaxResult.error(queueResponse.message)
        }
    }

    /**
     * 获取门诊的费用清单
     */
    @GetMapping("fee")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun fee(): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val response: Result<List<MenzhenFee>> = runBlocking(Dispatchers.IO) {
            BSoftService.me.getMenzhenFees(
                MenzhenFeeForm(
                    patientId = activePatient.patientNo,
                )
            )
        }

        if (!response.isOk()) {
            return response.toAjaxResult()
        }

        val menzhenFeeList = response.data.orEmpty()
            .groupBy { DateUtil.parse(it.billingDate).toLocalDateTime().toLocalDate() }
            .map { entry ->
                mapOf(
                    "billDate" to entry.key,
                    "totalCost" to entry.value.sumOf { it.totalMoney.toDouble() },
                    "list" to entry.value
                )
            }
        return AjaxResult.success(response.message, menzhenFeeList)
    }

    /**
     * 获取待缴费的医技列表
     */
    @GetMapping("unpaidMedicalTechnology")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun unpaidMedicalTechnology(): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val unpaidMedicalTechnologyResult: Result<List<UnpaidMedicalTechnology>> = runBlocking(Dispatchers.IO) {
            BSoftService.me.getUnpaidMedicalTechnology(
                UnpaidMedicalTechnologyForm(
                    patientId = activePatient.patientNo
                )
            )
        }

        return if (unpaidMedicalTechnologyResult.isOk()) {
            AjaxResult.success(unpaidMedicalTechnologyResult.data!!.sortedByDescending { it.costDate })
        } else {
            AjaxResult.error(unpaidMedicalTechnologyResult.message)
        }
    }

    /**
     * 获取门诊的账单（充值或退款记录）
     */
    @GetMapping("bill")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun bill(): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val balanceAndBillResponse: Result<List<MenzhenBill>> = runBlocking(Dispatchers.IO) {
            BSoftService.me.getMenzhenBillList(
                MenzhenBillForm(
                    patientId = activePatient.patientNo,
                )
            )
        }
        val ret: AjaxResult = balanceAndBillResponse.toAjaxResult()
        if (ret.isOk) {
            val menzhenBills = balanceAndBillResponse.data.orEmpty()
            // 分组充值退款
            val (rechargeBill, refundBill) = menzhenBills.partition { it.debitAmount.toDouble() > 0 }
            ret.data(
                mapOf(
                    "rechargeBill" to rechargeBill,
                    "refundBill" to refundBill,

                    )
            )
        }
        return ret
    }

    @PostMapping("refreshWxpayOrder")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun refreshWxpayOrder(@RequestParam zyPayNo: String): AjaxResult {
        synchronized(zyPayNo.intern()) {
            val payment: WxPayment = wxPaymentService.selectWxPaymentByZyPayNo(zyPayNo)
                ?: return AjaxResult.error("订单不存在[$zyPayNo]")

            if (!payment.wxPayNo.isNullOrBlank()) {
                // 本地订单状态已更新
                return AjaxResult.success(payment)
            }

            val wxPayService: WxPayService = SpringUtil.getBean(WxPayService::class.java)
            val wxPayOrderQueryResult: WxPayOrderQueryResult = wxPayService.queryOrder(null, payment.zyPayNo)
            wxPaymentService.updateOnPay(payment, wxPayOrderQueryResult)

            HisExt.recharge(payment.id)

            return AjaxResult.success(wxPaymentService.selectWxPaymentById(payment.id))
        }
    }

    /**
     * 充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("wxpay")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun wxpay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-menzhen-chongzhi", true)) {
            return AjaxResult.error("门诊充值功能正在升级维护，暂时使用，敬请谅解")
        }

//        if (amount < 100) {
//            return AjaxResult.error("最小充值金额为1元")
//        }
//
//        if (amount > 500000) {
//            return AjaxResult.error("最大充值金额为5000元")
//        }

        val activePatient = request.getCurrentPatient()
        val patientResult = BSoftService.getPatientInfoByJzCardNo(jzCardNo = activePatient.jzCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.MZ
        val outTradeNo: String = PaymentKit.newOrderId(serviceType)
        val wxPayMpOrderResult = WeixinExt.pay(
            serviceType = serviceType,
            amount = amount.toInt(),
            ip = ip,
            outTradeNo = outTradeNo,
            patient = activePatient
        ) { request, _ ->
            wxPaymentService.createAndSave(serviceType, activePatient, request)
        }

        return AjaxResult.success(
            mapOf(
                "appId" to wxPayMpOrderResult.appId,
                "timeStamp" to wxPayMpOrderResult.timeStamp,
                "nonceStr" to wxPayMpOrderResult.nonceStr,
                "package" to wxPayMpOrderResult.packageValue,
                "signType" to wxPayMpOrderResult.signType,
                "paySign" to wxPayMpOrderResult.paySign,
                "zyOrderNo" to outTradeNo
            )
        )
    }

    /**
     * 退款
     * @param zyPayNo 掌医充值订单号
     * @param amount 退款金额，以分为单位
     */
    @PostMapping("wxrefund")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun wxrefund(
        @RequestParam zyPayNo: String,
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-menzhen-tuikuan", true)) {
            return AjaxResult.error("门诊退款功能正在升级维护，暂时使用，敬请谅解")
        }

        if (amount <= 0) {
            return AjaxResult.error("退款金额不正确")
        }

        val activePatient = request.getCurrentPatient()
        synchronized(activePatient.patientNo.intern()) {
            val wxPayment = wxPaymentService.selectWxPaymentByZyPayNo(zyPayNo)
                ?: return AjaxResult.error("找不到指定的充值订单")

            if (wxPayment.jzCardNo != activePatient.jzCardNo) {
                return AjaxResult.error("当前就诊卡不能操作此订单")
            }

            if (wxPayment.type != ServiceType.MZ.name) {
                return AjaxResult.error("此订单不是门诊订单")
            }

            if (wxPayment.hisTradeStatus != Constants.RECHARGE_OK) {
                return AjaxResult.error("此订单不支持退款")
            }

            if (amount > wxPayment.unrefund) {
                return AjaxResult.error("退款金额超出订单限制")
            }

            val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
                jzCardNo = activePatient.jzCardNo,
            )
            if (!patientInfoResult.isOk()) {
                return AjaxResult.error("查询余额失败")
            }
            if (PaymentKit.yuanToFen(patientInfoResult.data?.cardBalance?.toDouble() ?: 0.0) < amount) {
                return AjaxResult.error("退款金额超出余额限制")
            }

            if (amount > wxPaymentService.calculateNetInAmount(LocalDate.now())) {
                return AjaxResult.error("商户余额不足，建议您两小时后重试")
            }

            val outRefundNo = PaymentKit.newOrderId(ServiceType.MZ)
            val hisRefundForm = MenzhenRefundForm(
                patientId = activePatient.patientNo,
                patientCard = activePatient.jzCardNo,
                creditAmount = PaymentKit.fenToYuan(amount),
                oldbOusinessSerialumber = wxPayment.businessSerialNumber,
                orderNumber = outRefundNo
            )
            val hisRefundResult: Result<MenzhenRefund> = runBlocking {
                BSoftService.me.menzhenRefund(hisRefundForm)
            }
            val wxRefund: WxRefund? = wxRefundService.createAndSave(wxPayment, amount, outRefundNo, hisRefundResult)

            if (hisRefundResult.isOk()) {
                // HIS退款成功，HIS充值退款服务连续失败的次数重置为0
                PaymentKit.CONTINUOUS_HIS_FAILURES.set(0)

                // 更新已退金额、可退金额
                wxPaymentService.updateOnRefund(wxPayment, amount)

                if (wxRefund != null) {

                    try {
                        WeixinExt.refund(
                            totalAmount = wxPayment.amount.toInt(),
                            amount = wxRefund.amount.toInt(),
                            wxPayNo = wxRefund.wxPayNo,
                            zyRefundNo = wxRefund.zyRefundNo
                        )
                    } catch (e: Exception) {
                        logger.error(e.message, e)
                    }
                }
            }

            return if (hisRefundResult.isOk()) {
                AjaxResult.success(hisRefundResult.data)
            } else {
                AjaxResult.error(hisRefundResult.message)
            }
        }
    }

    /**
     * 获取用于退款的账单，最多退半年内的订单
     */
    @GetMapping("billForRefund")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun billForRefund(
        @RequestParam endDate: Date?,
        @RequestParam days: Int?,
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()

        val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
            jzCardNo = currentPatient.jzCardNo,
        )
        if (!patientInfoResult.isOk()) {
            return AjaxResult.error("查询余额失败")
        }
        val balance: BigDecimal = patientInfoResult.data?.cardBalance?.toBigDecimal() ?: BigDecimal.ZERO

        if (PaymentKit.yuanToFen(balance) <= 0) {
            return AjaxResult.success(
                mapOf(
                    "totalAvailable" to balance,
                    "payments" to emptyList<Map<String, Any>>()
                )
            )
        }

        val endTime: Date = DateUtil.endOfDay(endDate ?: Date(), false)
        val startTime: Date = DateUtil.beginOfDay(DateUtil.offsetDay(endTime, -(days ?: 360)))
        val minStartTime: Date = DateUtil.beginOfDay(DateUtil.offsetDay(Date(), -360))

        val session = request.getClientSession()!!
        if (session.isWeixinSession) {
            val payments =
                wxPaymentService.selectListForRefund(
                    currentPatient.jzCardNo,
                    max(listOf(startTime, minStartTime)),
                    endTime
                )
            return AjaxResult.success(
                mapOf(
                    "totalAvailable" to balance,
                    "payments" to payments.filter {
                        (it?.unrefund ?: 0) > 0 && it.zyPayNo.startsWith(ServiceType.MZ.name)
                    }.map {
                        mapOf(
                            "time" to DateUtil.formatDateTime(it.createTime),
                            "zyPayNo" to it.zyPayNo,
                            "amount" to PaymentKit.fenToYuan(it.amount),
                            "exrefund" to PaymentKit.fenToYuan(it.exrefund),
                            "unrefund" to PaymentKit.fenToYuan(it.unrefund)
                        )
                    }
                ))
        }

        if (session.isAlipaySession) {
            val payments = alipayPaymentService.selectListForRefund(
                currentPatient.jzCardNo,
                max(listOf(startTime, minStartTime)),
                endTime
            )
            return AjaxResult.success(
                mapOf(
                    "totalAvailable" to balance,
                    "payments" to payments.filter {
                        (it?.unrefund ?: 0) > 0 && it.type == ServiceType.MZ.code
                    }.map {
                        mapOf(
                            "time" to DateUtil.formatDateTime(it.createTime),
                            "zyPayNo" to it.outTradeNo,
                            "amount" to PaymentKit.fenToYuan(it.totalAmount),
                            "exrefund" to PaymentKit.fenToYuan(it.exrefund),
                            "unrefund" to PaymentKit.fenToYuan(it.unrefund)
                        )
                    }
                ))
        }

        return AjaxResult.success(
            mapOf(
                "totalAvailable" to balance,
                "payments" to emptyList<Map<String, Any>>()
            )
        )
    }

    /**
     * 支付宝充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("alipay")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun alipay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-menzhen-chongzhi", true)) {
            return AjaxResult.error("门诊充值功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        if (amount > 500000) {
            return AjaxResult.error("最大充值金额为5000元")
        }

        val activePatient = request.getCurrentPatient()
        val patientResult = BSoftService.getPatientInfoByPatientId(patientId = activePatient.patientNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val serviceType = ServiceType.MZ
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            activePatient,
            serviceType,
            outTradeNo,
            amount,
            remark
        )
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }
        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = PaymentKit.fenToYuan(amount),
            openid = activePatient.openId,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )
        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(alipayTradeCreateResponse)
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    @PostMapping("refreshAlipayOrder")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun refreshAlipayOrder(@RequestParam outTradeNo: String): AjaxResult {
        synchronized(outTradeNo.intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(outTradeNo)
                ?: return AjaxResult.error("订单不存在[$outTradeNo]")

            val activePatient = request.getCurrentPatient()
            if (payment.jzCardNo != activePatient.jzCardNo) {
                return AjaxResult.error("您无权操作此订单")
            }

            val queryOrderResponse = AlipayService.queryOrder(payment.outTradeNo, "")
            alipayPaymentService.updateOnPay(payment, queryOrderResponse)

            HisExt.rechargeByAlipay(payment.id)

            return AjaxResult.success(alipayPaymentService.selectAlipayPaymentById(payment.id))
        }
    }

    @PostMapping("alipayRefund")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun alipayRefund(
        @RequestParam outTradeNo: String,
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-menzhen-tuikuan", true)) {
            return AjaxResult.error("门诊退款功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount <= 0) {
            return AjaxResult.error("退款金额不正确")
        }

        val activePatient = request.getCurrentPatient()
        synchronized(activePatient.openId.intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(outTradeNo)
                ?: return AjaxResult.error("找不到指定的充值订单")

            if (payment.patientId != activePatient.patientNo) {
                return AjaxResult.error("当前就诊卡不能操作此订单")
            }
            if (payment.type != ServiceType.MZ.code) {
                return AjaxResult.error("此订单不是门诊订单")
            }
            if (payment.hisTradeStatus != Constants.RECHARGE_OK) {
                return AjaxResult.error("此订单不支持退款")
            }
            if (amount > payment.unrefund) {
                return AjaxResult.error("退款金额超出订单限制")
            }

            val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByPatientId(
                patientId = activePatient.patientNo,
            )
            if (!patientInfoResult.isOk()) {
                return AjaxResult.error("查询余额失败")
            }
            if (PaymentKit.yuanToFen(patientInfoResult.data?.cardBalance?.toDouble() ?: 0.0) < amount) {
                return AjaxResult.error("退款金额超出余额限制")
            }

            val outRefundNo = PaymentKit.newOrderId(ServiceType.MZ)
            val refundResult: Result<MenzhenRefund> = runBlocking {
                BSoftService.me.menzhenRefund(
                    MenzhenRefundForm(
                        patientId = payment.patientId,
                        patientCard = payment.jzCardNo,
                        creditAmount = PaymentKit.fenToYuan(amount),
                        oldbOusinessSerialumber = payment.outTradeNo,
                        orderNumber = outRefundNo,
                        payType = "14"  // 支付宝
                    )
                )
            }
            val refund = AlipayRefund(payment, amount, refundResult)
            refund.outRefundNo = outRefundNo
            alipayRefundService.insertAlipayRefund(refund)

            if (refundResult.isOk()) {
                payment.exrefund = payment.exrefund + amount
                payment.unrefund = payment.totalAmount - payment.exrefund
                alipayPaymentService.updateAlipayPayment(payment)
                alipayRefundService.refundAlipay(refund)
                return AjaxResult.success()
            } else {
                return AjaxResult.error(refundResult.message)
            }
        }
    }

}
