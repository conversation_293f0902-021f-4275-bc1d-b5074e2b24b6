package space.lzhq.ph.controller

import com.alibaba.fastjson.JSONObject
import com.alipay.api.domain.MedicalNationalPayAuthInfo
import com.alipay.api.response.*
import com.ruoyi.common.constant.Constants.CITY_CODE_PREFIX
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.runBlocking
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.alipay.GrantType
import org.mospital.alipay.InsuredCity
import org.mospital.bsoft.mip.*
import org.mospital.jackson.JacksonKit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MipAlipayOrder
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IMipAlipayOrderService

@RestController
@RequestMapping("/api/alipayMip")
class AlipayMipController : BaseController() {

    @Autowired
    private lateinit var mipAlipayOrderService: IMipAlipayOrderService

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    /**
     * 获取待结算费用清单
     */
    @GetMapping("/pendingSettlements")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlements(): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val pendingSettlementResult: PendingSettlementResult = runBlocking {
            BSoftMipService.ALIPAY.getPendingSettlements(
                PendingSettlementForm(
                    patientId = currentPatient.patientNo
                )
            )
        }

        return if (pendingSettlementResult.isOk()) {
            AjaxResult.success(pendingSettlementResult.toPendingSettlements())
        } else {
            AjaxResult.error(pendingSettlementResult.message)
        }
    }

    /**
     * 支付宝授权
     * 小程序获取用户授权时，确保 scopes 为 ['nhsamp', 'auth_user']
     * 参见：https://opendocs.alipay.com/pre-open/02iqci
     */
    @PostMapping("/authAlipay")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun authAlipay(
        @RequestParam authCode: String,
    ): AjaxResult {
        val oauthTokenResponse: AlipaySystemOauthTokenResponse = AlipayService.getOrRefreshAccessToken(
            authCode = authCode,
            grantType = GrantType.AUTHORIZATION_CODE
        )
        if (!oauthTokenResponse.isSuccess) {
            return AjaxResult.error(oauthTokenResponse.subCode + "-" + oauthTokenResponse.subMsg)
        }

        val infoShareResponse: AlipayUserInfoShareResponse =
            AlipayService.getUserInfoShare(oauthTokenResponse.accessToken)
        logger.debug("AlipayUserInfoShareResponse：${JacksonKit.writeValueAsString(infoShareResponse)}")
        if (!infoShareResponse.isSuccess) {
            return AjaxResult.error(infoShareResponse.subCode + "-" + infoShareResponse.subMsg)
        }
        if (infoShareResponse.isCertified != "T") {
            return AjaxResult.error("您的支付宝账户未通过实名认证，无法获取实名信息")
        }
        if (infoShareResponse.certNo != request.getCurrentPatient().idCardNo) {
            return AjaxResult.error("您的支付宝账户与当前就诊人身份不一致，无法使用医保移动支付")
        }

        val mipAlipayOrder = MipAlipayOrder(authCode, oauthTokenResponse.accessToken, infoShareResponse)
        mipAlipayOrderService.save(mipAlipayOrder)

        return AjaxResult.success()
    }


    /**
     * 支付宝医保授权
     */
    @PostMapping("/authAlipayMip")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun authAlipayMip(
        @RequestParam authCode: String,
        @RequestParam latitude: String,
        @RequestParam longitude: String,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder? = mipAlipayOrderService.getOneByAuthCode(authCode)
        if (mipAlipayOrder == null || mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("获取支付宝授权失败")
        }

        val medicalAuthinfoAuthQueryResponse: AlipayCommerceMedicalAuthinfoAuthQueryResponse =
            AlipayService.commerceMedicalAuthInfoQuery(mipAlipayOrder.userId, mipAlipayOrder.accessToken)
        if (!medicalAuthinfoAuthQueryResponse.isSuccess) {
            return AjaxResult.error("${medicalAuthinfoAuthQueryResponse.subCode}: ${medicalAuthinfoAuthQueryResponse.subMsg}")
        }
        val medicalNationalPayAuthInfo: MedicalNationalPayAuthInfo = medicalAuthinfoAuthQueryResponse.data
        if (medicalNationalPayAuthInfo.authStas != "1") {
            return if (!medicalNationalPayAuthInfo.authUrl.isNullOrBlank()) {
                AjaxResult.success(
                    mapOf(
                        "state" to "MIP_AUTH_REDIRECT",
                        "url" to medicalNationalPayAuthInfo.authUrl
                    )
                )
            } else {
                AjaxResult.success(
                    mapOf(
                        "state" to "fail",
                        "msg" to "用户授权状态异常：${medicalNationalPayAuthInfo.authStas}"
                    )
                )
            }
        }

        // 获取电子医保凭证信息，主要用于获取参保地信息
        val cardInfoQuery: AlipayCommerceMedicalCardAuthQueryResponse =
            AlipayService.commerceMedicalCardAuthQuery(mipAlipayOrder.accessToken)
        if (!cardInfoQuery.isSuccess) {
            return AjaxResult.error("支付宝电子医保凭证信息获取失败：${cardInfoQuery.subMsg}")
        }
        if (cardInfoQuery.data?.status == "NOT_BIND" || cardInfoQuery.data?.insuredStatus == "NOT_INSURED") {
            return AjaxResult.error("您的支付宝账户未激活电子医保凭证")
        }

        val cities: List<InsuredCity> = AlipayService.parseInsuredCities(cardInfoQuery)
        var city = cities.firstOrNull { it.code.startsWith(CITY_CODE_PREFIX) }
        if (city == null) {
            city = cities.firstOrNull { it.isDefaultCity() }
        }
        if (city == null) {
            city = cities.firstOrNull()
        }
        if (city == null) {
            return AjaxResult.error("未查询到医保参保地信息")
        }

        if (!city.code.startsWith("65")) {
            return AjaxResult.error("目前仅支持新疆维吾尔自治区区医保，您当前的参保地为：${city.name}")
        }

        mipAlipayOrder.payAuthNo = medicalNationalPayAuthInfo.payAuthNo
        mipAlipayOrder.medicalCardInstId = medicalNationalPayAuthInfo.medicalCardInstId
        mipAlipayOrder.medicalCardId = medicalNationalPayAuthInfo.medicalCardId
        mipAlipayOrder.latitude = latitude
        mipAlipayOrder.longitude = longitude
        mipAlipayOrder.cityId = city.code
        mipAlipayOrderService.updateById(mipAlipayOrder)

        return AjaxResult.success(
            mapOf(
                "state" to "ok"
            )
        )
    }

    /**
     * 创建订单
     * @param acctUsedFlag 个人账户使用标志：0=不使用，1=使用
     */
    @PostMapping("createOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrder(
        @RequestParam settlementId: String,
        @RequestParam settlementItemIds: String,
        @RequestParam authCode: String,
        @RequestParam(required = false, defaultValue = "1") acctUsedFlag: Int,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder? = mipAlipayOrderService.getOneByAuthCode(authCode)
        if (mipAlipayOrder == null || mipAlipayOrder.userId != currentPatient.openId || mipAlipayOrder.payAuthNo.isNullOrBlank()) {
            return AjaxResult.error("获取支付宝授权失败")
        }

        val createOrderResult: CreateOrderResult = runBlocking {
            BSoftMipService.ALIPAY.createOrder(
                CreateOrderForm(
                    orgCode = BSoftMipSetting.ALIPAY.orgCode,
                    orgId = BSoftMipSetting.ALIPAY.orgId,
                    idNo = mipAlipayOrder.certNo!!,
                    userName = mipAlipayOrder.userName!!,
                    cfyjhm = settlementId,
                    feeIds = settlementItemIds,
                    payAuthNo = mipAlipayOrder.payAuthNo!!,
                    acctUsedFlag = acctUsedFlag,
                    payFlag = 2,
                    // Todo 医保参保地
                    insuredId = mipAlipayOrder.cityId,
                )
            )
        }

        return if (createOrderResult.isOk()) {
            val createOrderData: CreateOrderResult.Data = createOrderResult.data!!
            createOrderData.extraData.apply {
                this["payAuthNo"] = mipAlipayOrder.payAuthNo!!
                this["longitude"] = mipAlipayOrder.longitude
                this["latitude"] = mipAlipayOrder.latitude
            }

            mipAlipayOrder.payOrderId = createOrderData.payOrderId
            mipAlipayOrder.feeSumAmount = createOrderData.feeSumAmount
            mipAlipayOrder.ownPayAmount = createOrderData.ownPayAmount
            mipAlipayOrder.personalAccountPayAmount = createOrderData.personalAccountPayAmount
            mipAlipayOrder.fundPayAmount = createOrderData.fundPayAmount
            mipAlipayOrderService.updateById(mipAlipayOrder)

            AjaxResult.success(createOrderData)
        } else {
            AjaxResult.error(createOrderResult.message)
        }
    }

    /**
     * 支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     * @param medOrgOrd 医疗机构订单号，对应医保移动支付中心费用明细上传接口的入参 medOrgOrd
     */
    @PostMapping("payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun payOrder(
        @RequestParam payOrderId: String,
        @RequestParam medOrgOrd: String,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        try {
            val outTradeNo = PaymentKit.newOrderId(ServiceType.ZJ)
            val createOrderResponse: AlipayTradeAppPayResponse = AlipayService.createMipOrder(
                outTradeNo = outTradeNo,
                // 此处必须传入总金额，而不是自费金额
                totalAmount = mipAlipayOrder.feeSumAmount,
                medOrgOrd = medOrgOrd,
                payOrderId = mipAlipayOrder.payOrderId,
                payAuthNo = mipAlipayOrder.payAuthNo,
                medicalCardInstId = mipAlipayOrder.medicalCardInstId,
                medicalCardId = mipAlipayOrder.medicalCardId,
                subject = AlipaySetting.mipOrgName + "-" + ServiceType.ZJ.description,
                medicalRequestContent = JSONObject()
            )
            return if (createOrderResponse.isSuccess) {
                mipAlipayOrder.medOrgOrd = medOrgOrd
                mipAlipayOrder.outTradeNo = outTradeNo
                mipAlipayOrderService.updateById(mipAlipayOrder)

                AjaxResult.success("ok", createOrderResponse.body)
            } else {
                AjaxResult.error("下单失败：${createOrderResponse.subCode}-${createOrderResponse.subMsg}")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("下单失败：${e.message}")
        }
    }

    /**
     * 查询订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @GetMapping("queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOrder(@RequestParam payOrderId: String): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")
        if (mipAlipayOrder.userId != currentPatient.openId) {
            return AjaxResult.error("订单异常")
        }

        val queryOrderResponse: AlipayTradeQueryResponse =
            AlipayService.queryOrder(outTradeNo = mipAlipayOrder.outTradeNo)
        return if (queryOrderResponse.isSuccess) {
            mipAlipayOrder.tradeTime = queryOrderResponse.sendPayDate
            mipAlipayOrder.tradeNo = queryOrderResponse.tradeNo
            mipAlipayOrder.tradeStatus = queryOrderResponse.tradeStatus
            mipAlipayOrderService.updateById(mipAlipayOrder)

            AjaxResult.success(queryOrderResponse)
        } else {
            AjaxResult.error("${queryOrderResponse.subCode}-${queryOrderResponse.subMsg}")
        }
    }

    data class RefundOrderForm(
        val payOrderId: String,
    )

    @PostMapping("refundOrder")
    fun refundOrder(
        @RequestBody form: RefundOrderForm,
    ): AjaxResult {
        try {
            val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(form.payOrderId)
                ?: return AjaxResult.error("订单不存在")
            val refundResponse: AlipayTradeRefundApplyResponse = AlipayExt.refundAlipay(mipAlipayOrder)
            return if (refundResponse.isSuccess) {
                AjaxResult.success(refundResponse)
            } else {
                AjaxResult.error(refundResponse.subCode + ":" + refundResponse.subMsg)
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error("退款失败：${e.message}")
        }
    }

}
