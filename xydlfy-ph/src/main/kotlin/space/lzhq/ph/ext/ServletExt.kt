package space.lzhq.ph.ext

import com.ruoyi.common.utils.spring.SpringUtils
import org.springframework.http.HttpHeaders
import space.lzhq.ph.common.Values.CLIENT_SESSION
import space.lzhq.ph.common.Values.CLIENT_SESSION_LOADED
import space.lzhq.ph.common.Values.CURRENT_PATIENT
import space.lzhq.ph.common.Values.CURRENT_PSC_CARER
import space.lzhq.ph.common.Values.CURRENT_PSC_NURSE
import space.lzhq.ph.common.Values.CURRENT_ZHUYUAN_PATIENT
import space.lzhq.ph.domain.*
import space.lzhq.ph.service.ISessionService
import jakarta.servlet.http.HttpServletRequest

fun HttpServletRequest.getClientSession(): Session? {
    var session: Session? = this.getAttribute(CLIENT_SESSION) as Session?
    val sessionLoaded: Boolean = this.getAttribute(CLIENT_SESSION_LOADED) as Boolean? ?: false
    if (session == null && !sessionLoaded) {
        val sid: String? = this.getHeader(HttpHeaders.AUTHORIZATION)
        if (sid != null) {
            val wxSessionService = SpringUtils.getBean(ISessionService::class.java)
            session = wxSessionService.selectSessionById(sid)
            if (session != null) {
                this.setAttribute(CLIENT_SESSION_LOADED, true)
            }
        }
    }

    this.setAttribute(CLIENT_SESSION, session)

    return session
}

fun HttpServletRequest.getCurrentPatient(): Patient = getAttribute(CURRENT_PATIENT) as Patient
fun HttpServletRequest.getCurrentPscNurse(): PscNurseSession = getAttribute(CURRENT_PSC_NURSE) as PscNurseSession
fun HttpServletRequest.getCurrentPscCarer(): PscCarerSession = getAttribute(CURRENT_PSC_CARER) as PscCarerSession
fun HttpServletRequest.getCurrentZhuYuanPatient(): ZhuyuanPatient =
    getAttribute(CURRENT_ZHUYUAN_PATIENT) as ZhuyuanPatient
